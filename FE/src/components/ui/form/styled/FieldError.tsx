import clsx from 'clsx';
import {type FieldErrorProps} from 'react-aria-components';

import {AnimatePresence, motion} from 'motion/react';

import {FieldError as BaseFieldError} from '../FieldError';

export function FieldError({className, ...props}: FieldErrorProps) {
  return (
    <BaseFieldError
      {...props}
      className="contents"
    >
      {(validationError, renderProps) => (
        <AnimatePresence>
          {validationError && (
            <motion.div
              initial={{opacity: 0, height: 0, marginTop: 0}}
              animate={{opacity: 1, height: 'auto', marginTop: 4}}
              exit={{opacity: 0, height: 0, marginTop: 0}}
              className={clsx(
                typeof className === 'function'
                  ? className({...renderProps, defaultClassName: ''})
                  : className,
                'mt-1.5 block text-xs text-red-600'
              )}
            >
              {validationError}
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </BaseFieldError>
  );
}
