import {DatePicker, DatePickerProps} from 'react-aria-components';

import {DateValue} from '@internationalized/date';

import {FormField} from '../FormField';
import {MergeFormFieldProps} from './types';

export function DateFormField<T extends DateValue>(props: MergeFormFieldProps<DatePickerProps<T>>) {
  const {
    name,
    shouldUnregister,
    defaultValue = null,
    control,
    disabled,
    min,
    max,
    minLength,
    maxLength,
    required,
    pattern,
    deps,
    validate,
    ...restProps
  } = props;

  return (
    <FormField
      {...{
        name,
        shouldUnregister,
        defaultValue,
        control,
        disabled,
        min,
        max,
        minLength,
        maxLength,
        required,
        pattern,
        validate,
        deps,
      }}
    >
      <DatePicker {...restProps} />
    </FormField>
  );
}
