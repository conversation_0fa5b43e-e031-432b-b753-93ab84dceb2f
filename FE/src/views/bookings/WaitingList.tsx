import clsx from 'clsx';
import {useEffect, useMemo, useState} from 'react';
import {
  Checkbox,
  DialogTrigger,
  Input,
  ListBox,
  ListBoxItem,
  Popover,
  Select,
  SelectValue,
} from 'react-aria-components';
import {Button as RACButton} from 'react-aria-components';
import {useNavigate, useSearchParams} from 'react-router';

import {useQuery} from '@apollo/client';
import {useDebouncedValue} from '@mantine/hooks';
import {ColDef, iconSetMaterial, themeQuartz} from 'ag-grid-enterprise';
import {AgGridReact, CustomCellRendererProps} from 'ag-grid-react';
import {differenceInDays, format} from 'date-fns';
import {Calendar as CalendarIcon, ChevronDown, ChevronRight, ListFilter, PlusIcon} from 'lucide-react';
import {parseAsString, useQueryState} from 'nuqs';

import {SearchIconly} from '@/components/icons/SearchIconIconly.tsx';
import {CheckboxIcon} from '@/components/ui/CheckboxIcon.tsx';
import {But<PERSON>} from '@/components/ui/button';
import {getWaitingListData} from '@/graphql/bookings.ts';
import {getProceduresList, getUnitsList} from '@/graphql/lists.ts';
import {GET_SIDEBAR_MODULES} from '@/graphql/sidebar.ts';
import {getFullName} from '@/lib/utils.ts';

import {AddEditWaitingListDrawer} from './components/AddEditWaitingListDrawer.tsx';

type GroupingOption = 'urgency' | 'procedure' | 'patient' | 'none';

function WaitingList() {
  const {data: procedures} = useQuery(getProceduresList);
  const {data: unitsData} = useQuery(getUnitsList);
  const {data: sidebarModules} = useQuery(GET_SIDEBAR_MODULES);

  const isBookingsScheduleModuleActive = sidebarModules?.sidebar_modules?.some(
    (item) => item.title.toLowerCase() === 'bookings schedule' && item.enabled
  );

  const requestedTimeFrameMap = {
    'within-a-week': '< 7 days',
    'within-a-month': '7 - 30 days',
    'within-3-months': '31 - 90 days',
    'within-6-months': '91 - 180 days',
    'more-than-6-months': '> 180 days',
  };

  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [searchValue, setSearchValue] = useQueryState('search', parseAsString.withDefault(''));
  const [debouncedSearch] = useDebouncedValue(searchValue, 300);
  const [selectedUnit, setSelectedUnit] = useState();
  const [groupBy, setGroupBy] = useState<GroupingOption>('none');
  const {data, refetch} = useQuery(getWaitingListData, {
    variables: {
      unitId: (selectedUnit as any) ?? unitsData?.list_units[0]?.id,
      filter: debouncedSearch ? `%${debouncedSearch}%` : '%',
    },
  });

  const calculateDaysWaiting = (referralDate: string) => {
    const referral = new Date(referralDate);
    const today = new Date();
    return differenceInDays(today, referral);
  };

  const getProcedures = (procedureIds: number[]) => {
    return (
      procedures?.procedure
        ?.map((p) => procedureIds.includes(p.id) && `${p.seq_number}. ${p.name}`)
        ?.filter(Boolean)
        ?.join(', ') || undefined
    );
  };

  // Custom cell renderer for group rows
  const GroupCellRenderer = (params: CustomCellRendererProps & {[key: string]: any}) => {
    const [isExpanded, setIsExpanded] = useState(params.node.expanded || false);

    useEffect(() => {
      setIsExpanded(params.node.expanded);
    }, [params.node]);

    if (!params.node.group) return null;

    const groupKey = params.node.key;
    const childCount = params.node.allChildrenCount;
    const groupLevel = params.node.level;

    const handleToggleExpansion = () => {
      const newExpandedState = !isExpanded;
      setIsExpanded(newExpandedState);
      params.node.setExpanded(newExpandedState);
    };

    // Render different content based on grouping type
    const renderGroupContent = () => {
      if (groupBy === 'urgency') {
        return (
          <div
            className={clsx('mr-2 inline-flex items-center rounded-sm px-2 py-1 text-xs capitalize', {
              'bg-[#FEF5F4] text-[#FE0008]': groupKey === 'urgent',
              'bg-[#FCF9EE] text-[#8D6F00]': groupKey === 'soon',
              'bg-[#F7F7F7] text-[#787878]': groupKey === 'standard',
            })}
          >
            {groupKey}
          </div>
        );
      }

      return <div className="font-semibold">{groupKey}</div>;
    };

    return (
      <div
        className="flex h-9.5 cursor-pointer items-center font-normal"
        onClick={handleToggleExpansion}
        style={{paddingLeft: `${groupLevel * 20}px`}}
      >
        <div
          className={clsx(
            'mr-1.5 transform cursor-pointer text-neutral-500 transition-transform duration-200',
            isExpanded ? 'rotate-90' : 'rotate-0'
          )}
        >
          <ChevronRight className="h-4 w-4 text-neutral-800" />
        </div>
        <div className="flex items-center text-xs text-neutral-800">
          {renderGroupContent()}
          <div className="ml-2 text-neutral-600">({childCount})</div>
        </div>
      </div>
    );
  };

  const waitingListRecords = useMemo(() => {
    return data?.waiting_list?.map((item) => ({
      ...item,
      name: getFullName(item.pas_pt.pas_pt_names[0]),
      dob: item.pas_pt.dob,
      procedureNames: getProcedures(item.procedure_ids || []) || 'No procedures',
      daysWaiting: calculateDaysWaiting((item.referral_date as string) ?? item.created_at),
    }));
  }, [data?.waiting_list, procedures]);

  // Filter data by search (only by name)
  const filteredData = useMemo(() => {
    if (!waitingListRecords) return [];

    return waitingListRecords.filter((item) => {
      const search = debouncedSearch.toLowerCase();
      return item.name.toLowerCase().includes(search) || item?.urgency?.toLowerCase().includes(search);
    });
  }, [waitingListRecords, debouncedSearch]);

  const urgencyOrder = {urgent: 0, soon: 1, standard: 2};
  const sortedData = useMemo(() => {
    return filteredData.sort((a, b) => {
      return (
        urgencyOrder[a.urgency as keyof typeof urgencyOrder] -
        urgencyOrder[b.urgency as keyof typeof urgencyOrder]
      );
    });
  }, [filteredData]);

  // Column definitions for AG Grid - dynamic based on groupBy
  const columnDefs: ColDef[] = useMemo(() => {
    const baseColumns: ColDef[] = [
      // Requested Timeframe column (only for selectedUnit === 4)
      ...(selectedUnit === 4
        ? [
            {
              field: 'requested_timeframe',
              headerName: 'Requested Timeframe',
              valueGetter: (params: any) => {
                if (!params.data) return '';
                return params.data.requested_timeframe || '';
              },
              cellRenderer: (params: any) => {
                if (params.node.group) return null;
                return (
                  <div className="flex h-9.5 items-center text-xs">
                    {requestedTimeFrameMap[params.value as keyof typeof requestedTimeFrameMap]}
                  </div>
                );
              },
            },
          ]
        : []),
      {
        field: 'urgency',
        headerName: 'Urgency',
        rowGroup: groupBy === 'urgency',
        hide: groupBy === 'urgency',
        valueGetter: (params) => {
          if (!params.data) return params.node?.key || '';
          return params.data.urgency || 'standard';
        },
        comparator: (valueA: string, valueB: string) => {
          const urgencyOrder = {urgent: 0, soon: 1, standard: 2};
          return (
            urgencyOrder[valueA as keyof typeof urgencyOrder] -
            urgencyOrder[valueB as keyof typeof urgencyOrder]
          );
        },
        cellRenderer: (params: any) => {
          if (params.node.group || groupBy === 'urgency') return null;
          return (
            <div
              className={clsx('inline-flex items-center rounded-sm px-2 py-1 text-xs capitalize', {
                'bg-[#FEF5F4] text-[#FE0008]': params.data.urgency === 'urgent',
                'bg-[#FCF9EE] text-[#8D6F00]': params.data.urgency === 'soon',
                'bg-[#F7F7F7] text-[#787878]': params.data.urgency === 'standard',
              })}
            >
              {params.data.urgency || 'standard'}
            </div>
          );
        },
      },
      {
        field: 'daysWaiting',
        headerName: 'Days Waiting',
        valueGetter: (params) => {
          if (!params.data) return '';
          return params.data.daysWaiting;
        },
        cellRenderer: (params: any) => {
          if (params.node.group) return null;
          return (
            <div className="flex h-9.5 w-full items-center pl-16 text-right text-xs">{params.value}</div>
          );
        },
      },
      {
        field: 'referral_date',
        headerName: 'Referral Date',
        valueGetter: (params) => {
          if (!params.data) return '';
          return format(new Date(params.data.referral_date as string), 'dd MMM yyyy');
        },
        cellRenderer: (params: any) => {
          if (params.node.group) return null;
          return <div className="flex h-9.5 items-center text-xs">{params.value}</div>;
        },
      },
      {
        field: 'procedureNames',
        headerName: 'Procedure',
        rowGroup: groupBy === 'procedure',
        hide: groupBy === 'procedure',
        valueGetter: (params) => {
          if (!params.data) return params.node?.key || '';
          return params.data.procedureNames || 'No procedures';
        },
        cellRenderer: (params: any) => {
          if (params.node.group || groupBy === 'procedure') return null;
          return <div className="flex h-9.5 items-center text-xs">{params.value}</div>;
        },
      },
      {
        field: 'name',
        headerName: 'Patient Name',
        rowGroup: groupBy === 'patient',
        hide: groupBy === 'patient',
        valueGetter: (params) => {
          if (!params.data) return params.node?.key || '';
          return params.data.name;
        },
        cellRenderer: (params: any) => {
          if (params.node.group || groupBy === 'patient') return null;
          return <div className="flex h-9.5 items-center text-xs font-medium">{params.value}</div>;
        },
      },
      {
        field: 'dob',
        headerName: 'DOB',
        valueGetter: (params) => {
          if (!params.data) return '';
          return format(new Date(params.data.dob as string), 'dd MMM yyyy');
        },
        cellRenderer: (params: any) => {
          if (params.node.group) return null;
          return <div className="flex h-9.5 items-center text-xs">{params.value}</div>;
        },
      },
      {
        field: 'provisional_procedure_date',
        headerName: 'Prov. Procedure Date',
        valueGetter: (params) => {
          if (!params.data) return '';
          return format(
            new Date(
              params.data.study_request_item?.provisional_procedure_date ??
                (params.data.provisional_procedure_date as string)
            ),
            'dd MMM yyyy'
          );
        },
        cellRenderer: (params: any) => {
          if (params.node.group) return null;
          return <div className="flex h-9.5 items-center text-xs">{params.value}</div>;
        },
      },
      // Admission Type and Discharge Location columns (only for selectedUnit === 4)
      ...(selectedUnit === 4
        ? [
            {
              field: 'admission_type',
              headerName: 'Admission Type',
              valueGetter: (params: any) => {
                if (!params.data) return '';
                return params.data.admission_type || '';
              },
              cellRenderer: (params: any) => {
                if (params.node.group) return null;
                return <div className="flex h-9.5 items-center text-xs">{params.value}</div>;
              },
            },
            {
              field: 'discharge_location',
              headerName: 'Discharge Location',
              valueGetter: (params: any) => {
                if (!params.data) return '';
                return params.data.discharge_location || '';
              },
              cellRenderer: (params: any) => {
                if (params.node.group) return null;
                return <div className="flex h-9.5 items-center text-xs">{params.value}</div>;
              },
            },
          ]
        : []),
      {
        field: 'notes',
        headerName: 'Notes',
        valueGetter: (params) => {
          if (!params.data) return '';
          return params.data.notes || '';
        },
        cellRenderer: (params: any) => {
          if (params.node.group) return null;
          return <div className="flex h-9.5 items-center text-xs">{params.value}</div>;
        },
      },
      ...(isBookingsScheduleModuleActive
        ? [
            {
              field: 'actions',
              headerName: '',
              valueGetter: () => '',
              cellRenderer: (params: any) => {
                if (params.node.group) return null;
                return (
                  <RACButton className="text-brand-500 hover:text-brand-400 flex h-9.5 cursor-pointer items-center gap-x-1 px-2 text-xs font-medium">
                    <CalendarIcon className="h-4 w-4" />
                    Schedule
                  </RACButton>
                );
              },
              sortable: false,
              filter: false,
            },
          ]
        : []),
    ];

    return baseColumns;
  }, [groupBy, selectedUnit]);

  // AG Grid theme configuration
  const myTheme = themeQuartz.withPart(iconSetMaterial).withParams({
    fontFamily: 'Figtree, sans-serif',
    fontSize: 'var(--text-xs)',
    selectedRowBackgroundColor: 'color-mix(in oklab, var(--color-muted) 50%, transparent)',
    rowHoverColor: 'color-mix(in oklab, var(--color-muted) 50%, transparent)',
    columnBorder: false,
    headerFontSize: 'var(--text-xs)',
    headerFontWeight: 600,
    headerTextColor: 'var(--color-neutral-800)',
    headerHeight: 48,
    iconSize: 13,
    rowHeight: 38,
    headerBackgroundColor: 'var(--color-white)',
    borderColor: 'var(--color-neutral-200)',
    textColor: 'var(--color-neutral-700)',
    rangeSelectionBorderColor: 'var(--color-brand2-500)',
    rangeHeaderHighlightColor: 'var(--color-brand2-500)',
    accentColor: 'var(--color-brand2-400)',
    rowBorder: true,
    selectCellBorder: true,
    wrapperBorder: true,
    borderRadius: 0,
    cellEditingShadow: false,
    selectCellBackgroundColor: 'var(--color-white)',
    cellHorizontalPadding: 12,
  });

  const defaultColDef = {
    resizable: true,
    suppressMovable: false,
    suppressHeaderFilterButton: true,
    suppressHeaderMenuButton: true,
    sortable: true,
    flex: 1,
  };

  const autoGroupColumnDef = useMemo(
    () => ({
      headerName:
        groupBy === 'urgency'
          ? 'Urgency'
          : groupBy === 'procedure'
            ? 'Procedure'
            : groupBy === 'patient'
              ? 'Patient'
              : '',
      cellRenderer: GroupCellRenderer,
      cellRendererParams: {
        suppressCount: true,
      },
      minWidth: 300,
    }),
    [groupBy]
  );

  const groupByItems = [
    {id: 'urgency', textValue: 'Urgency'},
    {id: 'procedure', textValue: 'Procedure'},
    {id: 'patient', textValue: 'Patient'},
  ];

  const handleGroupByChange = (optionId: string, isChecked: boolean) => {
    if (isChecked) {
      // If checking a new option, set it as the groupBy
      setGroupBy(optionId as GroupingOption);
    } else {
      // If unchecking the current option, set to 'none'
      setGroupBy('none');
    }
  };

  const getGroupByDisplayText = () => {
    if (groupBy === 'none') return 'None';
    const item = groupByItems.find((item) => item.id === groupBy);
    return item ? item.textValue : 'None';
  };

  return (
    <div className="flex h-full flex-col gap-5">
      <div className="flex w-full items-center justify-between">
        <div className="flex w-full items-center gap-3">
          <Select
            aria-label="Departments"
            defaultSelectedKey={unitsData?.list_units[0]?.id ?? undefined}
            onSelectionChange={(s: any) => {
              setSelectedUnit(s);
              const newSearchParams = new URLSearchParams(searchParams);
              newSearchParams.set('unit', s);
              setSearchParams(newSearchParams, {replace: true});
            }}
          >
            <RACButton className="react-aria-Button h-8 w-65 rounded-sm">
              <SelectValue className="react-aria-SelectValue text-xs text-neutral-900 capitalize" />
              <ChevronDown />
            </RACButton>
            <Popover className="react-aria-Popover w-65">
              <ListBox items={unitsData?.list_units}>
                {(unit) => (
                  <ListBoxItem
                    className="react-aria-ListBoxItem text-xs"
                    id={unit.id}
                    textValue={String(unit.id)}
                  >
                    {unit.description}
                  </ListBoxItem>
                )}
              </ListBox>
            </Popover>
          </Select>
          <div className="relative w-90">
            <Input
              placeholder="Search patients name or urgency"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              className="peer ring-brand2-400/30 focus-visible:!border-brand2-500 h-8 w-full rounded-sm !border !border-neutral-300 bg-white px-4 pl-8 text-sm text-gray-600 outline-none placeholder:text-neutral-700 focus-visible:ring-3"
            />

            <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-1.75 left-2 text-neutral-400">
              <SearchIconly className="h-4.5 w-4.5" />
            </div>
          </div>
        </div>

        <Button
          className="react-aria-Button ml-auto h-8 shrink-0 rounded-sm"
          onPress={() => navigate(`/bookings/waiting-list/add?${searchParams}`)}
        >
          <PlusIcon className="h-4 w-4" />
          Add to waiting list
        </Button>
      </div>

      <DialogTrigger>
        <RACButton className="react-aria-Button h-6 w-43.5 cursor-pointer rounded-sm">
          <div className="flex items-center gap-1">
            <ListFilter className="h-3 w-3 text-neutral-900" />
            <span className="text-xs font-medium text-neutral-900">Group by:</span>
            <span className="text-xs text-neutral-900 capitalize">{getGroupByDisplayText()}</span>
            <ChevronDown className="ml-0.5 h-3 w-3 transition-transform duration-300 [[data-pressed='true']_&]:rotate-180" />
          </div>
        </RACButton>
        <Popover className="react-aria-Popover w-45 p-2.5">
          <div className="flex flex-col gap-2">
            {groupByItems.map((item) => (
              <div
                key={item.id}
                className="flex items-center gap-2"
              >
                <Checkbox
                  isSelected={groupBy === item.id}
                  onChange={(isChecked) => handleGroupByChange(item.id, isChecked)}
                  className="react-aria-Checkbox cursor-pointer"
                >
                  <CheckboxIcon className="checkbox h-4 w-4" />
                  <span className="text-sm text-neutral-800">{item.textValue}</span>
                </Checkbox>
              </div>
            ))}
          </div>
        </Popover>
      </DialogTrigger>

      <div className="h-full w-full">
        <AgGridReact
          key={groupBy}
          className="ag-waiting-list"
          rowData={sortedData}
          onRowClicked={(params) => {
            if (params.node.group) return;
            navigate(`/bookings/waiting-list/${params.data.id}?${searchParams}`);
          }}
          loading={!data}
          columnDefs={columnDefs}
          getRowId={(params) => String(params.data.id)}
          {...(groupBy !== 'none' && {
            autoGroupColumnDef: autoGroupColumnDef,
            groupDisplayType: 'singleColumn',
            groupDefaultExpanded: 0,
          })}
          defaultColDef={defaultColDef}
          suppressAggFuncInHeader
          animateRows
          pagination={false}
          theme={myTheme}
          domLayout="normal"
          overlayNoRowsTemplate={`<div class="text-center p-8">
            <div class="text-lg font-semibold text-neutral-900 mb-2">No waiting list entries found</div>
            <div class="text-sm text-neutral-600">Try refining your search or add a new record for waiting list.</div>
          </div>`}
        />
      </div>

      <AddEditWaitingListDrawer refetchWaitingList={refetch} />
    </div>
  );
}

export default WaitingList;
