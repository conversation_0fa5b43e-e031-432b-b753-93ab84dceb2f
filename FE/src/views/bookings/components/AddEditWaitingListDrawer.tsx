import clsx from 'clsx';
import {ComponentProps, useEffect, useState} from 'react';
import {
  ComboBox,
  DateInput,
  DateSegment,
  Dialog,
  Group,
  Heading,
  ListBox,
  ListBoxItem,
  Modal,
  Popover,
  Button as RACButton,
  SelectValue,
} from 'react-aria-components';
import {FieldValues, useForm} from 'react-hook-form';
import {useLocation, useNavigate, useParams, useSearchParams} from 'react-router';
import {AsyncListOptions, useAsyncList} from 'react-stately';

import {useMutation, useQuery} from '@apollo/client';
import {fromDate, getLocalTimeZone, now, toCalendarDate} from '@internationalized/date';
import {Calendar as CalenderIcon, ChevronDown, X} from 'lucide-react';

import {apolloClient} from '@/apollo-client.ts';
import {ProcedureTagList} from '@/components/AddProcedureForStudyItem.tsx';
import {Input, Label, TextArea} from '@/components/ui/Field.tsx';
import {But<PERSON>} from '@/components/ui/button';
import {Calendar} from '@/components/ui/calendar.tsx';
import {Form, FormField, SelectFormField, TextFormField} from '@/components/ui/form';
import {FieldError} from '@/components/ui/form/FieldError.tsx';
import {WatchState} from '@/components/ui/form/WatchState';
import {DateFormField} from '@/components/ui/form/fields/DateFormField.tsx';
import {
  addPatientToWaitingList,
  // getWaitingListData,
  getWaitingListPatientRecord,
  updatWaitingListPatientRecord,
} from '@/graphql/bookings.ts';
import {getProceduresList, getUnitsList} from '@/graphql/lists.ts';
import {searchPatientsQuery} from '@/graphql/patients.ts';
import authStore from '@/store/auth.store.ts';
import {Patient} from '@/store/patient';

export function AddEditWaitingListDrawer(props: ComponentProps<typeof Modal> & { refetchWaitingList: () => void; }) {
  const {waitingListId} = useParams();
  const isAddMode = waitingListId === undefined;

  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const {data: procedures} = useQuery(getProceduresList);
  const {data: unitsData} = useQuery(getUnitsList);
  const {data: waitingRecordDetail} = useQuery(getWaitingListPatientRecord, {
    variables: {id: parseInt(waitingListId ?? '')},
  });

  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const regex = /^\/bookings\/waiting-list\/(add|\d+)$/;
  const match = location.pathname.match(regex);

  const defaultSelectedUnit = searchParams.get('unit')
    ? Number(searchParams.get('unit'))
    : unitsData?.list_units[0]?.id;

  const form = useForm();
  const [addPatientToWaitingListMutation, {loading: isSubmitting}] = useMutation(addPatientToWaitingList);
  const [updateWaitingListMutation, {loading: isUpdating}] = useMutation(updatWaitingListPatientRecord);

  const patientsList = useAsyncList<Patient>({
    getKey: (p) => p.patientid,
    load: patientsListLoader,
  });

  function handleSubmit(data: FieldValues) {
    try {
      if (isAddMode) {
        addPatientToWaitingListMutation({variables: {waiting_list: data}});
      } else {
        updateWaitingListMutation({variables: {id: parseInt(waitingListId ?? ''), waiting_list: data}});
      }
    } catch (e) {
      console.log('error: ', e);
    }
  }

  function handleTransformData(data: FieldValues) {
    return {
      ...data,
      patientid: parseInt(data.patientid),
      referral_date: data.referral_date?.toString(),
      provisional_procedure_date: data.provisional_procedure_date?.toString(),
      procedure_ids: Array.from(data.procedure_ids),
      admission_type: data?.unit_id == 4 ? data?.admission_type : null,
      discharge_location: data?.unit_id == 4 ? data?.discharge_location : null,
      requested_timeframe: data?.unit_id == 4 ? data?.requested_timeframe : null,
    };
  }

  function handleReset() {
    form.setValue('patientid', null);
    form.setValue('referral_date', null);
    form.setValue('procedures', []);

    setSelectedPatient(null);
    patientsList.setFilterText('');
  }

  const admissionTypes = [
    {id: 'inpatient', description: 'Inpatient'},
    {id: 'outpatient', description: 'Outpatient'},
    {id: 'day', description: 'Day'},
    {id: 'emergency', description: 'Emergency'},
    {id: 'multi-day', description: 'Multi-day'},
  ];

  const dischargeLocations = [
    {id: 'home', description: 'Home'},
    {id: 'nursing home', description: 'Nursing Home'},
    {id: 'external hospital', description: 'External Hospital'},
    {id: 'rehabilitation center', description: 'Rehabilitation Center'},
    {id: 'other', description: 'Other'},
  ];

  const urgencies = [
    {id: 'urgent', description: 'Urgent'},
    {id: 'soon', description: 'Soon'},
    {id: 'standard', description: 'Standard'},
  ];

  useEffect(() => {
    if (waitingRecordDetail?.waiting_list[0]?.patientid) {
      const patient = waitingRecordDetail?.waiting_list?.[0]?.pas_pt?.pas_pt_names?.[0];
      const patientName = patient
        ? [patient.title ? `${patient.title}.` : '', patient.surname ?? '', patient.firstname ?? '']
            .filter(Boolean)
            .join(' ')
            .replace(/\s*,\s*$/, '')
        : '';

      const defaultPatient = new Patient(waitingRecordDetail?.waiting_list[0]);
      patientsList.setFilterText(patientName);
      setSelectedPatient(defaultPatient);
    }

    // const defaultReferralDate = waitingRecordDetail?.waiting_list[0]?.referral_date
    //   ? toCalendarDate(
    //       fromDate(new Date(waitingRecordDetail?.waiting_list[0]?.referral_date), getLocalTimeZone())
    //     )
    //   : null;
    //
    // const defaultProvisionalProcedureDate = waitingRecordDetail?.waiting_list[0]?.provisional_procedure_date
    //   ? toCalendarDate(
    //       fromDate(
    //         new Date(waitingRecordDetail?.waiting_list[0]?.provisional_procedure_date),
    //         getLocalTimeZone()
    //       )
    //     )
    //   : null;

    // form.setValue('patientid', waitingRecordDetail?.waiting_list[0]?.patientid ?? null);
    // form.setValue('referral_date', defaultReferralDate);
    // form.setValue('provisional_procedure_date', defaultProvisionalProcedureDate);
    // form.setValue('procedure_ids', waitingRecordDetail?.waiting_list[0]?.procedure_ids ?? []);
    // form.setValue('notes', waitingRecordDetail?.waiting_list[0]?.notes ?? null);
    // form.setValue('admission_type', waitingRecordDetail?.waiting_list[0]?.admission_type ?? null);
    // form.setValue('discharge_location', waitingRecordDetail?.waiting_list[0]?.discharge_location ?? null);
    // form.setValue('requested_timeframe', waitingRecordDetail?.waiting_list[0]?.requested_timeframe ?? null);
    // form.setValue('urgency', waitingRecordDetail?.waiting_list[0]?.urgency ?? null);

    return () => handleReset();
  }, [waitingRecordDetail?.waiting_list[0]]);

  const requestedTimeframes = [
    {id: 'within-a-week', description: 'Within a Week'},
    {id: 'within-a-month', description: 'Within a Month'},
    {id: 'within-3-months', description: 'Within 3 Months'},
    {id: 'within-6-months', description: 'Within 6 Months'},
    {id: 'more-than-6-months', description: 'More than 6 Months'},
  ];

  return (
    <Modal
      isDismissable
      isOpen={!!match}
      onOpenChange={() => {
        navigate('/bookings/waiting-list');
      }}
      className="react-aria-Drawer"
      {...props}
    >
      <Dialog className="react-aria-Dialog flex min-h-full flex-col gap-y-4">
        <div className="no-inset shrink-0 border-b border-neutral-200 text-neutral-800">
          <Heading slot="title">{isAddMode ? 'Add to Waiting List' : 'Edit Patients Waiting Record'}</Heading>
          <RACButton slot="close">
            <X />
          </RACButton>
        </div>

        <Form
          className="flex flex-1 flex-col"
          // control={form}
          transformData={handleTransformData}
          onSubmit={handleSubmit}
          onSubmitSuccess={() => {
            props?.refetchWaitingList();
            handleReset();
            navigate('/bookings/waiting-list');
          }}
        >
          <div className="flex-1">
            <div className="my-4 text-xs leading-snug font-bold tracking-wide text-neutral-800 uppercase">
              Referral Details
            </div>

            <div className="grid grid-cols-3 gap-4">
              <FormField
                name="patientid"
                defaultValue={waitingRecordDetail?.waiting_list[0]?.patientid ?? null}
                required
              >
                <ComboBox
                  items={patientsList.items}
                  inputValue={patientsList.filterText}
                  onInputChange={patientsList.setFilterText}
                  onSelectionChange={(key) => {
                    if (key) {
                      const patient = patientsList.getItem(key) ?? null;
                      patientsList.setFilterText(patient?.fullName ?? '');
                      setSelectedPatient(patient);
                    } else {
                      setSelectedPatient(null);
                    }
                  }}
                  className="react-aria-ComboBox col-span-2"
                >
                  <Label className="text-neutral-700">Name</Label>
                  <div className="relative">
                    <Input placeholder="Search Patients" />
                    <RACButton className="absolute inset-y-0 right-2 z-20 flex items-center">
                      <ChevronDown className="size-4" />
                    </RACButton>
                  </div>
                  <Popover>
                    <ListBox>
                      {(item: Patient) => <ListBoxItem id={item.patientid}>{item.fullName}</ListBoxItem>}
                    </ListBox>
                  </Popover>
                </ComboBox>
              </FormField>

              <DateFormField
                name="referral_date"
                defaultValue={
                  waitingRecordDetail?.waiting_list[0]?.referral_date
                    ? toCalendarDate(
                        fromDate(
                          new Date(waitingRecordDetail?.waiting_list[0]?.referral_date),
                          getLocalTimeZone()
                        )
                      )
                    : toCalendarDate(now(getLocalTimeZone()))
                }
              >
                <Label className="text-neutral-700">Referral Date</Label>
                <Group>
                  <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
                  <RACButton className="react-aria-Button">
                    <CalenderIcon
                      className="h-4 w-4"
                      color="currentColor"
                    />
                  </RACButton>
                </Group>
                <Popover className="react-aria-Popover w-max">
                  <Dialog className="p-4">
                    <Calendar />
                  </Dialog>
                </Popover>
                <FieldError />
              </DateFormField>

              {selectedPatient && (
                <div className="contents divide-x divide-neutral-200">
                  <div>
                    {/* todo: handle MRN Style */}
                    <div className="text-xs leading-relaxed font-semibold text-neutral-800">MRN</div>
                    <div className="mt-1 text-sm text-neutral-800">{selectedPatient?.ur}</div>
                  </div>
                  <div>
                    <div className="text-xs leading-relaxed font-semibold text-neutral-800">Gender</div>
                    <div className="mt-1 text-sm text-neutral-800">
                      {selectedPatient?.genderResolved?.description}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs leading-relaxed font-semibold text-neutral-800">DOB</div>
                    <div className="mt-1 text-sm text-neutral-800">
                      {selectedPatient?.dob} ({selectedPatient?.ageToday?.toFixed(1)} yrs)
                    </div>
                  </div>
                </div>
              )}
            </div>

            <hr className="mt-6 mb-4 text-neutral-100" />

            <div className="my-4 text-xs leading-snug font-bold tracking-wide text-neutral-800 uppercase">
              Urgency
            </div>

            <div>
              <SelectFormField
                name="urgency"
                aria-label="Urgency"
                placeholder="Select Urgency"
                className="react-aria-Select placeholder:text-neutral-200"
                defaultValue={waitingRecordDetail?.waiting_list[0]?.urgency ?? 'standard'}
              >
                <RACButton className="react-aria-Button h-9 w-full rounded-sm">
                  <SelectValue className="react-aria-SelectValue text-xs text-neutral-900 capitalize placeholder:!text-neutral-200" />
                  <ChevronDown />
                </RACButton>
                <Popover>
                  <ListBox
                    items={urgencies}
                    className="react-aria-ListBox"
                  >
                    {(urgency) => (
                      <ListBoxItem
                        className="react-aria-ListBoxItem px-1.5 text-xs"
                        id={urgency.id}
                        textValue={String(urgency.id)}
                      >
                        <div
                          className={clsx('w-fit rounded-sm px-2 py-1', {
                            'bg-[#FEF5F4] text-[#FE0008]': urgency.id === 'urgent',
                            'bg-[#FCF9EE] text-[#8D6F00]': urgency.id === 'soon',
                            'bg-[#F7F7F7] text-[#787878]': urgency.id === 'standard',
                          })}
                        >
                          {urgency.description}
                        </div>
                      </ListBoxItem>
                    )}
                  </ListBox>
                </Popover>
              </SelectFormField>
            </div>

            <hr className="mt-6 mb-4 text-neutral-100" />

            <div className="my-4 text-xs leading-snug font-bold tracking-wide text-neutral-800 uppercase">
              Procedure Details
            </div>

            <div>
              <div className="mb-4">
                <Label className="text-neutral-700">Unit</Label>
                <SelectFormField
                  name="unit_id"
                  aria-label="Labs"
                  defaultValue={waitingRecordDetail?.waiting_list[0]?.unit_id ?? defaultSelectedUnit}
                  placeholder="Select Unit"
                >
                  <RACButton className="react-aria-Button h-9 w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-xs text-neutral-900 capitalize" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox items={unitsData?.list_units}>
                      {(unit) => (
                        <ListBoxItem
                          className="react-aria-ListBoxItem text-sm"
                          id={unit.id}
                          textValue={String(unit.id)}
                        >
                          {unit.description}
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </SelectFormField>
              </div>

              <WatchState name="unit_id">
                {(unitId) => {
                  return (
                    (unitId === 4 || defaultSelectedUnit === 4) && (
                      <div>
                        <div className="my-4 flex w-full items-center gap-x-4">
                          <SelectFormField
                            defaultValue={waitingRecordDetail?.waiting_list[0]?.admission_type}
                            name="admission_type"
                            placeholder="Select Admission Type"
                          >
                            <Label className="text-neutral-700">Admission Type</Label>
                            <RACButton className="react-aria-Button h-9 w-56 rounded-sm">
                              <SelectValue className="react-aria-SelectValue text-xs text-neutral-900 capitalize" />
                              <ChevronDown />
                            </RACButton>
                            <Popover>
                              <ListBox items={admissionTypes}>
                                {(item) => (
                                  <ListBoxItem
                                    className="react-aria-ListBoxItem text-sm"
                                    id={item.id}
                                    textValue={String(item.id)}
                                  >
                                    {item.description}
                                  </ListBoxItem>
                                )}
                              </ListBox>
                            </Popover>
                          </SelectFormField>

                          <SelectFormField
                            defaultValue={waitingRecordDetail?.waiting_list[0]?.discharge_location}
                            name="discharge_location"
                            placeholder="Select Discharge Location"
                          >
                            <Label className="text-neutral-700">Discharge Location</Label>
                            <RACButton className="react-aria-Button h-9 w-56 rounded-sm">
                              <SelectValue className="react-aria-SelectValue text-xs text-neutral-900 capitalize" />
                              <ChevronDown />
                            </RACButton>
                            <Popover>
                              <ListBox items={dischargeLocations}>
                                {(item) => (
                                  <ListBoxItem
                                    className="react-aria-ListBoxItem text-sm"
                                    id={item.id}
                                    textValue={String(item.id)}
                                  >
                                    {item.description}
                                  </ListBoxItem>
                                )}
                              </ListBox>
                            </Popover>
                          </SelectFormField>
                        </div>

                        <SelectFormField
                          defaultValue={waitingRecordDetail?.waiting_list[0]?.requested_timeframe}
                          placeholder="Select Requested Timeframe"
                          name="requested_timeframe"
                          className="react-aria-Select my-4"
                        >
                          <Label className="text-neutral-700">Requested Timeframe</Label>
                          <RACButton className="react-aria-Button h-9 w-full rounded-sm">
                            <SelectValue className="react-aria-SelectValue text-xs text-neutral-900 capitalize" />
                            <ChevronDown />
                          </RACButton>
                          <Popover>
                            <ListBox items={requestedTimeframes}>
                              {(item) => (
                                <ListBoxItem
                                  className="react-aria-ListBoxItem text-sm"
                                  id={item.id}
                                  textValue={String(item.id)}
                                >
                                  {item.description}
                                </ListBoxItem>
                              )}
                            </ListBox>
                          </Popover>
                        </SelectFormField>
                      </div>
                    )
                  );
                }}
              </WatchState>

              <WatchState name="unit_id">
                {(unitId) => {
                  const updatedProcedures =
                    procedures?.procedure?.filter(
                      (proc) => proc.unit_id === (unitId ?? defaultSelectedUnit)
                    ) || [];
                  return (
                    <ProcedureTagList
                      name="procedure_ids"
                      availableProcedures={updatedProcedures as any}
                      selectedProcedureIds={waitingRecordDetail?.waiting_list[0]?.procedure_ids ?? undefined}
                    />
                  );
                }}
              </WatchState>

              <DateFormField
                name="provisional_procedure_date"
                granularity="day"
                className="react-aria-DatePicker my-4"
                defaultValue={
                  waitingRecordDetail?.waiting_list[0]?.provisional_procedure_date
                    ? toCalendarDate(
                        fromDate(
                          new Date(waitingRecordDetail?.waiting_list[0]?.provisional_procedure_date),
                          getLocalTimeZone()
                        )
                      )
                    : null
                }
              >
                <Label className="text-neutral-700">Provisional Date</Label>
                <Group>
                  <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
                  <RACButton className="react-aria-Button">
                    <CalenderIcon
                      className="h-4 w-4"
                      color="currentColor"
                    />
                  </RACButton>
                </Group>
                <Popover className="react-aria-Popover w-max">
                  <Dialog className="p-4">
                    <Calendar />
                  </Dialog>
                </Popover>
                <FieldError />
              </DateFormField>

              <TextFormField
                name="notes"
                className="react-aria-TextField"
                defaultValue={waitingRecordDetail?.waiting_list[0]?.notes ?? ''}
              >
                <Label className="text-neutral-700">Test/Clinical Notes</Label>
                <TextArea size="md" />
              </TextFormField>
            </div>
          </div>

          <div className="border-neutral-00 sticky bottom-0 z-50 w-full border-t bg-white p-4">
            <Button
              className="w-full"
              type="submit"
              isDisabled={isSubmitting || isUpdating}
            >
              {isSubmitting || isUpdating
                ? 'Submitting...'
                : isAddMode
                  ? 'Add To Waiting List'
                  : 'Update Waiting Record'}
            </Button>
          </div>
        </Form>
      </Dialog>
    </Modal>
  );
}

export const patientsListLoader: AsyncListOptions<Patient, string>['load'] = async ({filterText}) => {
  const {data} = await apolloClient.query({
    query: searchPatientsQuery,
    variables: {
      searchText: filterText ?? '',
      genderCodes: null,
      siteId: authStore.tokenSiteId?.toString(),
      limit: 20,
      offset: 0,
    },
    fetchPolicy: 'network-only',
  });

  return {
    items: data.search_pas_pt.map((p) => new Patient(p as any, false)),
  };
};
