import {useEffect} from 'react';
import {<PERSON>ton, ListBox, ListBoxItem, Popover, Select, SelectValue} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {ChevronDown} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import NIL from '@/components/NIL.tsx';
import {DataSheet, DataSheetCell, DataSheetRow} from '@/components/data-sheet';
import {
  ParameterZScoreRange,
  ZScorePlotAxis,
  ZScorePlotParamDot,
  ZScorePlotParamRow,
} from '@/features/studies-rft/ZScorePlot.tsx';
import {CommitableParameterField} from '@/features/studies-rft/components/CommitableParameterField.tsx';
import {ComputedParameterValue} from '@/features/studies/stores/base.store.tsx';
import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';
import {getPreferenceField} from '@/graphql/preferences.ts';

export const SpirometryFormSection = observer(({rftStore}: {rftStore: RftStore}) => {
  const {data} = useQuery(getPreferenceField, {variables: {fieldName: 'Test condition'}});
  const spirometryStore = rftStore.spirometryStore;

  useEffect(() => {
    if (data?.prefs_fields?.[0] && !rftStore.spirometryStore.sp1Condition) {
      const defaultItem = data?.prefs_fields[0]?.prefs_fielditems.find(
        (e) => e.prefs_id === data?.prefs_fields[0]?.default_fielditem_id
      );

      rftStore.spirometryStore.setProperty('sp1Condition', defaultItem?.fielditem ?? null);
    }
  }, [data]);

  return (
    <div>
      <div className="flex flex-wrap items-start justify-between gap-x-2">
        <div className="flex items-start gap-x-2">
          <div className="w-32">
            <div className="mb-2 h-[calc(var(--row-height)+4px)]" />
            <div className="flex h-[calc(var(--row-height)+4px)] items-center justify-end text-right text-[10px] font-semibold text-neutral-800 uppercase">
              Spirometry
            </div>
            {spirometryStore.sp1.values.map((value, index) => (
              <div
                key={index}
                className="flex h-(--row-height) items-center justify-end"
              >
                <div className="truncate text-right text-(length:--font-size) font-semibold text-neutral-600">
                  {(() => {
                    if (value.parameter?.description === 'FER') {
                      return 'FEV1/FVC';
                    }

                    return value.parameter?.description ?? ''
                  })()}{' '}
                  {value.parameter?.configAwareUnits && <>[{value.parameter?.configAwareUnits}]</>}
                </div>
              </div>
            ))}
          </div>

          <div>
            <div className="py-0.875 mb-2 grid h-[calc(var(--row-height)+4px)] grid-cols-[4.25rem_4.25rem_4.25rem_4.25rem] items-center rounded border border-neutral-300 text-[10px]/[1.3] font-semibold text-neutral-600 uppercase">
              <p className="pr-1.5 text-right">Ref</p>
              <p className="pr-1.5 text-right">Result</p>
              <div className="flex items-center justify-end gap-x-1 pr-1.5 text-right text-[10px]/[1.3] whitespace-nowrap">
                <div className="bg-chart-1 size-2 rounded" />
                Z-score
              </div>
              <p className="pr-1.5 text-right">%Pred</p>
            </div>

            <Select
              selectedKey={spirometryStore.sp1Condition}
              onSelectionChange={(val) => {
                spirometryStore.setProperty('sp1Condition', val as string);
                spirometryStore.upsertDB('r_pre_condition');
              }}
              aria-label="Spirometry 1 test condition"
              isDisabled={!rftStore.isEditing}
            >
              <Button className="react-aria-Button -mb-0.25 h-[calc(var(--row-height)+4px)] w-full rounded-none rounded-t-sm px-3 py-1">
                <SelectValue className="react-aria-SelectValue text-[9px]/[1.3]" />
                <ChevronDown
                  className="h-3 w-3 text-gray-400"
                  aria-hidden="true"
                />
              </Button>
              <Popover>
                <ListBox items={data?.prefs_fields[0]?.prefs_fielditems ?? []}>
                  {(item) => <ListBoxItem id={item.fielditem?.toString()}>{item.fielditem}</ListBoxItem>}
                </ListBox>
              </Popover>
            </Select>

            <DataSheet className="grid-cols-[4.25rem_4.25rem_4.25rem_4.25rem]">
              {spirometryStore.sp1.values.map((value, index) => {
                const isNil = value instanceof ComputedParameterValue && !value.result;

                return (
                  <DataSheetRow key={index}>
                    <DataSheetCell className="text-right italic">{value.refValue ?? <NIL />}</DataSheetCell>
                    <DataSheetCell
                      data-computed={value instanceof ComputedParameterValue}
                      className="text-right"
                    >
                      {isNil ? (
                        <NIL />
                      ) : (
                        <CommitableParameterField
                          paramValue={value}
                          rftStore={rftStore}
                        />
                      )}
                    </DataSheetCell>
                    <DataSheetCell className="text-right">
                      {value.zscoreFormatted ? value.zscoreFormatted : <NIL />}
                    </DataSheetCell>
                    <DataSheetCell className="text-right">
                      {value.predPercent ? (
                        `${Math.round(value.predPercent)}%`
                      ) : (
                        <NIL />
                      )}
                    </DataSheetCell>
                  </DataSheetRow>
                );
              })}
            </DataSheet>
          </div>

          <div>
            <div className="py-0.875 py-0.875 mb-2 grid h-[calc(var(--row-height)+4px)] grid-cols-[4.25rem_4.25rem_4.25rem_4.25rem_4.25rem] items-center rounded border border-neutral-300 px-1 text-[10px]/[1.3] font-semibold text-neutral-600 uppercase">
              <p className="pr-1.5 text-right">Result</p>
              <div className="flex items-center justify-end gap-x-1 pr-1.5 text-right text-[10px]/[1.3] whitespace-nowrap">
                <div className="bg-chart-2 size-2 rounded-[1px]" />
                Z-score
              </div>
              <p className="pr-1.5 text-right">%Pred</p>
              <p className="pr-1.5 text-right">Change</p>
              <p className="pr-1.5 text-right">%Chg</p>
            </div>

            <Select
              selectedKey={spirometryStore.sp2Condition}
              onSelectionChange={(val) => {
                spirometryStore.setProperty('sp2Condition', val as string);
                spirometryStore.upsertDB('r_post_condition');
              }}
              aria-label="Spirometry 2 test condition"
              isDisabled={!rftStore.isEditing}
            >
              <Button className="react-aria-Button -mb-0.25 h-[calc(var(--row-height)+4px)] w-full rounded-none rounded-t-sm px-3 py-1">
                <SelectValue className="react-aria-SelectValue text-[9px]/[1.3]" />
                <ChevronDown
                  className="h-3 w-3 text-gray-400"
                  aria-hidden="true"
                />
              </Button>
              <Popover>
                <ListBox items={data?.prefs_fields[0]?.prefs_fielditems ?? []}>
                  {(item) => <ListBoxItem id={item.fielditem?.toString()}>{item.fielditem}</ListBoxItem>}
                </ListBox>
              </Popover>
            </Select>

            <DataSheet className="grid-cols-[4.25rem_4.25rem_4.25rem_4.25rem_4.25rem]">
              {spirometryStore.sp2.values.map((value, index) => {
                const baselineValue = spirometryStore.sp1.values[index];
                // %change = (post - pre) / mpv
                const change =
                  value.result && baselineValue.result ? value.result - baselineValue.result : undefined;
                const percentChange =
                  value.result && baselineValue.result
                    ? Math.round(((change ?? 0) / (baselineValue.predResult?.mpv ?? 0)) * 100)
                    : undefined;

                const isNil = value instanceof ComputedParameterValue && !value.result;

                return (
                  <DataSheetRow key={index}>
                    <DataSheetCell
                      data-computed={value instanceof ComputedParameterValue}
                      className="text-right"
                    >
                      {isNil ? (
                        <NIL />
                      ) : (
                        <CommitableParameterField
                          paramValue={value}
                          rftStore={rftStore}
                        />
                      )}
                    </DataSheetCell>
                    <DataSheetCell className="text-right">
                      {value.zscoreFormatted ? value.zscoreFormatted : <NIL />}
                    </DataSheetCell>
                    <DataSheetCell className="text-right">
                      {value.predPercent ? (
                        `${Math.round(value.predPercent)}%`
                      ) : (
                        <NIL />
                      )}
                    </DataSheetCell>
                    <DataSheetCell className="text-right">
                      {change !== undefined ? `${change > 0 ? '+' : ''}${change.toFixed(2)}` : <NIL />}
                    </DataSheetCell>

                    <DataSheetCell className="text-right">
                      {percentChange !== undefined ? `${percentChange ?? ''}%` : <NIL />}
                    </DataSheetCell>
                  </DataSheetRow>
                );
              })}
            </DataSheet>
          </div>
        </div>

        <div id="sp-zscore-plot" className="mt-4.5 flex max-w-70 flex-1 shrink-0 items-center gap-x-2">
          <div className="w-full">
            <div className="flex items-center justify-between text-[10px]/[1.3] font-medium text-neutral-600">
              <div className="uppercase">Z-Score Plot</div>

              <div className="flex items-center gap-x-2">
                <div className="flex items-center gap-x-1">
                  <div className="bg-chart-1 size-2 rounded" />
                  <div>{spirometryStore.sp1Condition ?? 'Baseline'}</div>
                </div>

                <div className="flex items-center gap-x-1">
                  <div className="bg-chart-2 size-2 rounded-[1px]" />
                  <div>{spirometryStore.sp2Condition ?? 'Post BD (Salb MDI)'}</div>
                </div>
              </div>
            </div>

            <ZScorePlotAxis className="my-1" />

            {spirometryStore.sp1.values.map((value, index) => {
              const value2 = spirometryStore.sp2.values[index];

              if (!value.refValue) return <div className="h-3.25" />;

              return (
                <ZScorePlotParamRow
                  key={value.parameter?.description}
                  className="my-0.5 overflow-hidden"
                >
                  <ParameterZScoreRange parameterValue={value} />
                  {value?.zScore && !isNaN(value?.zScore) && <ZScorePlotParamDot val={value?.zScore ?? 0} />}
                  {value2?.zScore && !isNaN(value2?.zScore) && (
                    <ZScorePlotParamDot
                      className="bg-chart-2 rounded-none"
                      val={value2?.zScore ?? 0}
                    />
                  )}
                </ZScorePlotParamRow>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
});
