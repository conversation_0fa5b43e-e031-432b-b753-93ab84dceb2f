import {<PERSON>Field} from 'react-aria-components';

import {observer} from 'mobx-react-lite';
import {CartesianGrid, Line, LineChart, ReferenceLine, ResponsiveContainer, XAxis, YAxis} from 'recharts';

import {DataSheet, DataSheetCell, DataSheetRow} from '@/components/data-sheet';
import {Input} from '@/components/ui/Field.tsx';
import {BhrProcedureStore} from '@/features/studies-bhr/store.ts';

const DoseResponseChart = observer(({store}: {store: BhrProcedureStore}) => {
  const pdThreshold = parseFloat(store.protocol.pd_threshold) || 20;
  const thresholdY = 100 - pdThreshold;

  const yMin = 50;
  const yMax = 120;
  const yStep = 10;

  void store.doseSchedule.schedules.forEach((s) => void s.responseValue);

  return (
    <div className="h-56 w-full">
      <ResponsiveContainer>
        <LineChart data={store.doseSchedule.schedules}>
          <CartesianGrid
            strokeDasharray="3 3"
            stroke="#e0e0e0"
          />
          <XAxis
            type="category"
            dataKey="dose_xaxis_label"
            tick={{ fontSize: 10 }}
          />
          <YAxis
            type="number"
            domain={[yMin, yMax]}
            allowDataOverflow={true}
            tick={{ fontSize: 10 }}
            ticks={Array.from({length: Math.floor((yMax - yMin) / yStep) + 1}, (_, i) => yMin + i * yStep)}
          />
          <ReferenceLine
            y={thresholdY}
            stroke="#0066cc"
            strokeDasharray="5 5"
            // label={{value: `PD${pdThreshold}`, position: 'middle'}}
          />

          <Line
            animationDuration={500}
            type="linear"
            dataKey="responseValue"
            stroke="#dc2626"
            strokeWidth={1.5}
            dot={false}
            connectNulls={false}
            strokeDasharray="5 5"
          />

          <Line
            animationDuration={500}
            type="linear"
            dataKey={(v) => (['−1', '-1', '100'].includes(v.dose_number) ? null : v.responseValue)}
            stroke="#dc2626"
            strokeWidth={1.5}
            dot={{stroke: '#dc2626', strokeWidth: 1.5, r: 3}}
            connectNulls={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
});

export const DoseResponse = observer(({store}: {store: BhrProcedureStore}) => (
  <div className="flex items-start gap-8">
    <div>
      <div className="flex h-[calc(var(--row-height)+4px)] items-center text-[10px] font-semibold text-neutral-800 uppercase">
        Dose Response to {store.protocol.p_agent}
      </div>
      <div className="py-0.875 mb-2 grid h-[calc(var(--row-height)+4px)] grid-cols-[7rem_5rem_5rem] items-center rounded border border-neutral-300 text-[10px]/[1.3] font-semibold text-neutral-600 uppercase">
        <p className="px-1.5 text-left">Dose</p>
        <p className="px-1.5 text-right">
          {store.protocol.p_parameter} [{store.protocol.p_parameter_units?.trim()}]
        </p>
        <p className="px-1.5 text-right">% Control</p>
      </div>
      <DataSheet className="grid-cols-[7rem_5rem_5rem]">
        {store.doseSchedule.schedules.map((doseSchedule) => (
          <DataSheetRow key={doseSchedule.doseid}>
            <DataSheetCell className="text-left">{doseSchedule.dose_xaxis_label}</DataSheetCell>
            <DataSheetCell className="text-right">
              <NumberField
                aria-label="Test"
                value={doseSchedule.resultValue ?? undefined}
                formatOptions={{
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }}
                onChange={(v) => doseSchedule.setResult(v)}
              >
                <Input
                  placeholder="—"
                  className="react-aria-Input text-right tabular-nums"
                />
              </NumberField>
            </DataSheetCell>
            <DataSheetCell className="text-right tabular-nums">{doseSchedule.response} %</DataSheetCell>
          </DataSheetRow>
        ))}
      </DataSheet>
      <div>PD{store.protocol.pd_threshold}: {store.pdx}</div>
    </div>
    <div className="w-full">
      <div className="flex h-[calc(var(--row-height)+4px)] items-center text-[10px] font-semibold text-neutral-800 uppercase">
        {store.protocol.plot_title}
      </div>
      <DoseResponseChart store={store} />
    </div>
  </div>
));
