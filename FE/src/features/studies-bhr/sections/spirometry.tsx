import {observer} from 'mobx-react-lite';

import NIL from '@/components/NIL.tsx';
import {DataSheet, DataSheetCell, DataSheetRow} from '@/components/data-sheet';
import {type ProcedureTestStore} from '@/features/studies-bhr/store.ts';
import {
  ParameterZScoreRange,
  ZScorePlotAxis,
  ZScorePlotParamDot,
  ZScorePlotParamRow,
} from '@/features/studies-rft/ZScorePlot.tsx';
import {CommitableParameterField} from '@/features/studies-rft/components/CommitableParameterField.tsx';
import {ComputedParameterValue} from '@/features/studies/stores/base.store.tsx';

export const MannitolSpirometrySection = observer(({store}: {store: ProcedureTestStore}) => {
  if (!store) return null;
  
  return (
    <div>
      <div className="flex flex-wrap items-start justify-between gap-x-2">
        <div className="flex items-start gap-x-2">
          <div className="w-22">
            <div className="flex h-[calc(var(--row-height)+4px)] mb-2 pb-px items-center justify-end text-right text-[10px] font-semibold text-neutral-800 uppercase">
              Spirometry
            </div>
            {store.parameterValues.map((value, index) => (
              <div
                key={index}
                className="flex h-(--row-height) items-center justify-end"
              >
                <div className="truncate text-right text-(length:--font-size) font-semibold text-neutral-600">
                  {(() => {
                    if (value.parameter?.description === 'FER') {
                      return 'FEV1/FVC';
                    }

                    return value.parameter?.description ?? '';
                  })()}{' '}
                  {value.parameter?.configAwareUnits && <>[{value.parameter?.configAwareUnits}]</>}
                </div>
              </div>
            ))}
          </div>

          <div>
            <div className="py-0.875 mb-2 grid h-[calc(var(--row-height)+4px)] grid-cols-[4.25rem_4.25rem_4.25rem_4.25rem] items-center rounded border border-neutral-300 text-[10px]/[1.3] font-semibold text-neutral-600 uppercase">
              <p className="pr-1.5 text-right">Ref</p>
              <p className="pr-1.5 text-right">Result</p>
              <div className="flex items-center justify-end gap-x-1 pr-1.5 text-right text-[10px]/[1.3] whitespace-nowrap">
                <div className="bg-chart-1 size-2 rounded" />
                Z-score
              </div>
              <p className="pr-1.5 text-right">%Pred</p>
            </div>

            <DataSheet className="grid-cols-[4.25rem_4.25rem_4.25rem_4.25rem]">
              {store.parameterValues.map((value, index) => {
                const isNil = value instanceof ComputedParameterValue && !value.result;

                return (
                  <DataSheetRow key={index}>
                    <DataSheetCell className="text-right italic">{value.refValue ?? <NIL />}</DataSheetCell>
                    <DataSheetCell
                      data-computed={value instanceof ComputedParameterValue}
                      className="text-right"
                    >
                      {isNil ? (
                        <NIL />
                      ) : (
                        <CommitableParameterField
                          paramValue={value}
                          rftStore={{isEditing: true}}
                        />
                      )}
                    </DataSheetCell>
                    <DataSheetCell className="text-right">
                      {value.zscoreFormatted ? value.zscoreFormatted : <NIL />}
                    </DataSheetCell>
                    <DataSheetCell className="text-right">
                      {value.predPercent ? `${Math.round(value.predPercent)}%` : <NIL />}
                    </DataSheetCell>
                  </DataSheetRow>
                );
              })}
            </DataSheet>
          </div>
        </div>

        <div
          id="sp-zscore-plot"
          className="flex max-w-70 flex-1 shrink-0 items-center gap-x-2"
        >
          <div className="w-full">
            <div className="flex items-center justify-between text-[10px]/[1.3] font-medium text-neutral-600">
              <div className="uppercase">Z-Score Plot</div>
            </div>

            <ZScorePlotAxis className="my-1" />

            {store.parameterValues.map((value, index) => {
              if (!value.refValue)
                return (
                  <div
                    className="h-3.25"
                    key={index}
                  />
                );

              return (
                <ZScorePlotParamRow
                  key={value.parameter?.description}
                  className="my-0.5 overflow-hidden"
                >
                  <ParameterZScoreRange parameterValue={value} />
                  {value?.zScore && !isNaN(value?.zScore) && <ZScorePlotParamDot val={value?.zScore ?? 0} />}
                </ZScorePlotParamRow>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
});
