import {graphql} from "@/graphql";

export const GET_PROV_TEST = graphql(`
  query GetProvTest($id: Int!) {
    prov_test(where: {provid: {_eq: $id}}) {
      bdstatus
      lab
      lastupdatedby_prov
      pd
      pd_decimalplaces
      pd_method
      pd_threshold
      plot_title
      plot_xscaling_type
      plot_xtitle
      plot_ymax
      plot_ymin
      plot_ystep
      r_bl_fef2575
      r_bl_fer
      r_bl_fev1
      r_bl_fvc
      r_bl_pef
      r_bl_vc
      report_amendedby
      report_authorisedby
      report_reportedby
      report_status
      report_verifiedby
      scientist
      testtype
      report_amendeddate
      report_authoriseddate
      report_reporteddate
      report_verifieddate
      patientid
      protocolid
      provid
      sessionid
      device_info
      report_amendednotes
      report_text
      technicalnotes
      lastupdated_prov
      testtime

      prov_testdata {
        response
        result
        doseid
      }
    }
  }
`);