import clsx from 'clsx';
import {Suspense, useEffect, useState} from 'react';
import {Switch} from 'react-aria-components';
import {useLocation, useNavigate, useParams, useSearchParams} from 'react-router';

import {useMutation, useQuery} from '@apollo/client';
import {Redo2, Undo2} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import PaperIcon from '@/assets/iconly/Paper.svg?react';
import SearchChatIcon from '@/assets/iconly/SearchChat.svg?react';
import {Button} from '@/components/ui/button.tsx';
import {useSidebar} from '@/components/ui/sidebar.tsx';
import {Tabs, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs.tsx';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/tooltip.tsx';
import {useRftReport} from '@/features/studies-rft/report';
import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';
import {undoStack} from '@/features/undo-stack';
import {getEthnicities, getRFTGenders, updatePatientEthnicity, updatePatientGender} from '@/graphql/patients';
import {getPatientsDetailData, updateSessionHeight, updateSessionWeight} from '@/graphql/patients.ts';
import {useGlobalStoreLoader} from '@/store/global.store.ts';

import '../studies-rft/styles.css';
import {MannitolSpirometrySection} from './sections/spirometry.tsx';
import {BhrProcedureStore} from "@/features/studies-bhr/store.ts";
import {GET_PROV_TEST} from "@/features/studies-bhr/queries.ts";
import {DoseResponse} from "@/features/studies-bhr/sections/DoseResponse.tsx";

function TestSections({store}: {store: BhrProcedureStore}) {
  useGlobalStoreLoader();
  return (
    <div
      id="mannitol-tests"
      className="relative space-y-3 rounded border border-neutral-200 bg-white p-4"
    >
      <MannitolSpirometrySection store={store.spirometryTest!} />
      <DoseResponse store={store} />
    </div>
  );
}

const MannitolDetail = observer(() => {
  const {id, patientId} = useParams<{id: string; patientId: string}>();
  const {toggleSidebar, state} = useSidebar();
  const {isLoading} = useRftReport();
  const [searchParams] = useSearchParams();
  const editParam = searchParams.get('edit');
  const isEditing = editParam === 'true';
  useQuery(getPatientsDetailData, {variables: {patientId: +patientId!}});
  const {data: procedure} = useQuery(GET_PROV_TEST, {variables: {id: +id!}});

  const [store] = useState(() => {
    return new BhrProcedureStore();
  });

  useEffect(() => {
    undoStack.reset();

    return () => {
      undoStack.reset();
    };
  }, [id, isEditing]);

  useEffect(() => {
    if (state === 'expanded') {
      toggleSidebar();
    }
  }, []);

  useEffect(() => {
    if (procedure?.prov_test?.[0]) {
      store.fromQueryResult(procedure?.prov_test?.[0]);
      store.loadConfig().then(() => {
        store.fromQueryResult(procedure?.prov_test?.[0]);
      });
    }
  }, [procedure?.prov_test?.[0]]);

  (window as any).store = store;

  return (
    <div className="relative">
      {/*<ReportPatientInfo testSession={rftStore.testSession} />*/}
      <Tabs defaultValue="results">
        <div className="mb-4 flex items-center gap-x-5">
          {/*<MannitolToolbar store={store} />*/}
          <div className="flex-1" />

          <Button
            variant="outlined"
            size="small"
            // isDisabled={isLoading}
            // isPending={isLoading}
            // onPress={async () => {
            //   createReport({rftStore}).then(({url, fileName}) => {
            //     window.open(url, '_blank');
            //     const link = document.createElement('a');
            //     link.href = url;
            //     link.download = fileName || 'MannitolReport.pdf';
            //     document.body.appendChild(link);
            //     link.click();
            //     document.body.removeChild(link);
            //   });
            // }}
          >
            <PaperIcon />
            {isLoading ? 'Compiling Report...' : 'View Report'}
          </Button>
        </div>

        <div className="flex items-start gap-x-3">
          <div className="study-results w-278 shrink-0">
            <Suspense fallback={<div>Loading...</div>}>
              <TabsContent
                className="mt-0"
                value="results"
              >
                <TestSections store={store} />
              </TabsContent>
            </Suspense>
          </div>
          {/*<ReportSidebar rftStore={rftStore} />*/}
        </div>
      </Tabs>
    </div>
  );
});

const MannitolToolbar = observer(({rftStore}: {rftStore: RftStore}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const [tempHeight, setTempHeight] = useState('');
  const [tempWeight, setTempWeight] = useState('');
  const [updateSessionHeight_] = useMutation(updateSessionHeight);
  const [updateSessionWeight_] = useMutation(updateSessionWeight);
  const [updateGender_] = useMutation(updatePatientGender, {
    refetchQueries: [getPatientsDetailData],
  });
  const [updateEthnicity_] = useMutation(updatePatientEthnicity, {
    refetchQueries: [getPatientsDetailData],
  });
  const {data: gendersData} = useQuery(getRFTGenders);
  const {data: ethnicitiesData} = useQuery(getEthnicities);
  const ethnicityOptions = ethnicitiesData?.pred_ref_ethnicities || [];
  const genderOptions = gendersData?.pred_ref_genders || [];
  const [tempGender, setTempGender] = useState('');
  const [tempEthnicity, setTempEthnicity] = useState('');
  const [missingHeight, setMissingHeight] = useState(false);
  const [missingWeight, setMissingWeight] = useState(false);
  const [missingGender, setMissingGender] = useState(false);
  const [missingEthnicity, setMissingEthnicity] = useState(false);
  const [hasEvaluatedPatientData, setHasEvaluatedPatientData] = useState(false);
  const session = rftStore.testSession;
  const patientId = session?.patientid;

  const {data: patientData, loading: isPatientDataLoading} = useQuery(getPatientsDetailData, {
    variables: {patientId},
    skip: !patientId,
  });

  useEffect(() => {
    const session = rftStore.testSession;
    const patient = session?.patient;

    if (
      !rftStore.isHydrated ||
      !rftStore.isEditing ||
      hasEvaluatedPatientData ||
      isPatientDataLoading ||
      !session ||
      !patient
    )
      return;

    const searchParams = new URLSearchParams(location.search);
    searchParams.set('edit', 'true');

    const height = String(session.height ?? '').trim();
    const weight = String(session.weight ?? '').trim();
    const gender = String(
      patientData?.pas_pt?.[0]?.gender_forrfts_code ?? session?.patient?.gender_forrfts_code ?? ''
    ).trim();
    const ethnicity = String(
      patientData?.pas_pt?.[0]?.race_forrfts_code ?? session?.patient?.race_forrfts_code ?? ''
    ).trim();

    const isMissingHeight = !height || height.toLowerCase() === 'nan';
    const isMissingWeight = !weight || weight.toLowerCase() === 'nan';
    const isMissingGender = !gender || gender.toLowerCase() === 'nan';
    const isMissingEthnicity = !ethnicity || ethnicity.toLowerCase() === 'nan';

    if (isMissingHeight || isMissingWeight || isMissingGender || isMissingEthnicity) {
      setTempHeight(isMissingHeight ? '' : height);
      setMissingHeight(isMissingHeight);
      setTempWeight(isMissingWeight ? '' : weight);
      setMissingWeight(isMissingWeight);
      setTempGender(isMissingGender ? '' : gender);
      setMissingGender(isMissingGender);
      setTempEthnicity(isMissingEthnicity ? '' : ethnicity);
      setMissingEthnicity(isMissingEthnicity);
      setShowModal(true);
    }

    navigate(
      {
        pathname: location.pathname,
        search: searchParams.toString(),
      },
      {replace: true}
    );

    setHasEvaluatedPatientData(true);
  }, [
    rftStore.isEditing,
    rftStore.isHydrated,
    hasEvaluatedPatientData,
    patientData?.pas_pt?.[0]?.gender_forrfts_code,
    patientData?.pas_pt?.[0]?.race_forrfts_code,
    location.pathname,
    location.search,
    navigate,
  ]);

  useEffect(() => {
    if (rftStore.isEditing) {
      setHasEvaluatedPatientData(false);
    }
  }, [rftStore.isEditing]);

  return (
    <>
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div className="w-full max-w-sm rounded bg-white p-6 shadow-xl">
            <h2 className="mb-4 text-lg font-semibold">Enter Patient RFT Info</h2>
            <div className="space-y-4">
              {missingHeight && (
                <div>
                  <label className="mb-1 block text-sm">Height (cm)</label>
                  <input
                    type="number"
                    className="w-full rounded border px-3 py-2 text-sm"
                    value={tempHeight}
                    onChange={(e) => setTempHeight(e.target.value)}
                    placeholder="Height in cm"
                  />
                </div>
              )}

              {missingWeight && (
                <div>
                  <label className="mb-1 block text-sm">Weight (kg)</label>
                  <input
                    type="number"
                    className="w-full rounded border px-3 py-2 text-sm"
                    value={tempWeight}
                    onChange={(e) => setTempWeight(e.target.value)}
                    placeholder="Weight in kg"
                  />
                </div>
              )}

              {missingGender && (
                <div>
                  <label className="mb-1 block text-sm">Gender</label>
                  <select
                    value={tempGender}
                    onChange={(e) => setTempGender(e.target.value)}
                    className="w-full rounded border px-3 py-2 text-sm"
                  >
                    <option value="">Select Gender</option>
                    {genderOptions.map((g) => (
                      <option
                        key={g.id}
                        value={g.id}
                      >
                        {g.description}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {missingEthnicity && (
                <div>
                  <label className="mb-1 block text-sm">Ethnicity</label>
                  <select
                    value={tempEthnicity}
                    onChange={(e) => setTempEthnicity(e.target.value)}
                    className="w-full rounded border px-3 py-2 text-sm"
                  >
                    <option value="">Select Ethnicity</option>
                    {ethnicityOptions.map((e) => (
                      <option
                        key={e.ethnicityid}
                        value={e.ethnicityid}
                      >
                        {e.description}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>

            <div className="mt-6 flex justify-end gap-2">
              <Button
                size="small"
                onPress={() => setShowModal(false)}
              >
                Cancel
              </Button>
              <Button
                size="small"
                onPress={async () => {
                  const parsedHeight = parseFloat(tempHeight);
                  const parsedWeight = parseFloat(tempWeight);

                  if (!isNaN(parsedHeight)) {
                    rftStore.testSession.height = String(parsedHeight);
                    try {
                      await updateSessionHeight_({
                        variables: {
                          sessionId: rftStore.testSession?.sessionid,
                          height: String(parsedHeight),
                        },
                      });
                    } catch (err) {
                      console.error(err);
                      alert('❌ Height update failed');
                    }
                  }

                  if (!isNaN(parsedWeight)) {
                    rftStore.testSession.weight = String(parsedWeight);
                    try {
                      await updateSessionWeight_({
                        variables: {
                          sessionId: rftStore.testSession?.sessionid,
                          weight: String(parsedWeight),
                        },
                      });
                    } catch (err) {
                      console.error(err);
                      alert('❌ Weight update failed');
                    }
                  }

                  if (tempGender) {
                    rftStore.testSession.patient.gender_forrfts_code = tempGender;
                    try {
                      await updateGender_({
                        variables: {
                          patientId: rftStore.testSession?.patient?.patientid,
                          genderCode: tempGender,
                        },
                      });
                    } catch (err) {
                      console.error(err);
                      alert('❌ Gender update failed');
                    }
                  }

                  if (tempEthnicity) {
                    rftStore.testSession.patient.race_forrfts_code = tempEthnicity;
                    try {
                      await updateEthnicity_({
                        variables: {
                          patientId: rftStore.testSession?.patient?.patientid,
                          raceCode: tempEthnicity,
                        },
                      });
                    } catch (err) {
                      console.error(err);
                      alert('❌ Ethnicity update failed');
                    }
                  }
                  setShowModal(false);
                }}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      )}

      <TabsList className="flex w-fit justify-start">
        <TabsTrigger
          className="flex cursor-pointer gap-x-2 font-normal text-neutral-700 data-[state=active]:font-medium data-[state=active]:text-neutral-800"
          value="results"
        >
          <SearchChatIcon className="size-4.5" />
          Results
        </TabsTrigger>
      </TabsList>

      <Tooltip>
        <TooltipTrigger asChild>
          <div id="mannitol-edit-mode-toggle">
            <Switch
              isSelected={rftStore.isEditing}
              onChange={(isSelected) => {
                rftStore.setProperty('isEditing', isSelected);
              }}
              isDisabled={!rftStore.isReportEditable}
              isReadOnly={!rftStore.isReportEditable}
            >
              <div>Edit Mode</div>
              <div className="flex h-7.5 items-center gap-x-2 rounded border border-neutral-300 px-2">
                <div className="w-5 text-center text-[10px] text-neutral-600">
                  <div className="block [[data-selected]_&]:hidden">OFF</div>
                  <div className="hidden [[data-selected]_&]:block">ON</div>
                </div>
                <div className="indicator" />
              </div>
            </Switch>
          </div>
        </TooltipTrigger>
        {!rftStore.isReportEditable && (
          <TooltipContent>You cannot edit a completed report. Try amending instead.</TooltipContent>
        )}
      </Tooltip>

      {rftStore.isEditing && (
        <div className="flex items-center gap-x-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={!undoStack.canUndo}
                onClick={() => undoStack.undo()}
                className={clsx(
                  'text-brand-600 cursor-pointer disabled:text-neutral-500',
                  !undoStack.canUndo && 'cursor-default'
                )}
              >
                <Undo2 className="size-4.5" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Undo</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={!undoStack.canRedo}
                onClick={() => undoStack.redo()}
                className={clsx(
                  'text-brand-600 cursor-pointer disabled:text-neutral-500',
                  !undoStack.canRedo && 'cursor-default'
                )}
              >
                <Redo2 className="size-4.5" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Redo</TooltipContent>
          </Tooltip>
        </div>
      )}
    </>
  );
});

export default MannitolDetail;
