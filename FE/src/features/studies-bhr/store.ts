import {makeAutoObservable, runInAction} from 'mobx';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import {axiosInstance} from '@/axios.ts';
import {applyMixins} from '@/lib/mixin.ts';
import {DisposableStoreMixin} from '@/store/disposable.store.ts';
import {Parameter} from '@/store/parameter.ts';

import {
  DisposableStoreWithUpsert,
  ParameterContext,
  ParameterValue,
  PredMetadataStore,
  RefType,
} from '../studies/stores/base.store.tsx';
import {GET_PROV_TEST} from './queries.ts';

export interface DoseScheduleItem {
  dose_canskip: boolean;
  dose_cumulative: string | null;
  dose_discrete: string | null;
  dose_number: string;
  dose_time_min: string;
  dose_xaxis_label: string;
  doseid: number;
}

export interface ProvTestData {
  response: string | null;
  result: string | null;
  doseid: number;
}

export interface ProtocolConfig {
  p_agent: string;
  p_agent_units: string;
  p_description: string;
  p_dose_effect: string;
  p_menulabel: string;
  p_method_reference: string;
  p_parameter: string;
  p_parameter_response: string;
  p_parameter_units: string;
  p_post_drug: string;
  p_title: string;
  pd_decimalplaces: number;
  pd_method: string;
  pd_threshold: string;
  plot_title: string;
  plot_xscaling_type: string;
  plot_xtitle: string;
  plot_ymax: string;
  plot_ymin: string;
  plot_ystep: string;
}

export interface TestConfig {
  [paramName: string]: {
    db_field: string;
    param: {
      decimalplaces: number;
      decimalplaces_si: number;
      description: string;
      longname: string;
      testid: number;
      units: string;
      units_convert_trad_to_si: number;
      units_si: string;
    };
    ref_type: string;
  };
}

export interface BhrConfig {
  dose_schedule: DoseScheduleItem[];
  protocol: ProtocolConfig;
  tests: {
    [testName: string]: TestConfig;
  };
}

export class ProtocolStore {
  p_agent!: string;
  p_agent_units!: string;
  p_description!: string;
  p_dose_effect!: string;
  p_menulabel!: string;
  p_method_reference!: string;
  p_parameter!: string;
  p_parameter_response!: string;
  p_parameter_units!: string;
  p_post_drug!: string;
  p_title!: string;
  pd_decimalplaces!: number;
  pd_method!: string;
  pd_threshold!: string;
  plot_title!: string;
  plot_xscaling_type!: string;
  plot_xtitle!: string;
  plot_ymax!: string;
  plot_ymin!: string;
  plot_ystep!: string;

  constructor() {
    makeAutoObservable(this);
  }

  fromConfig(protocol: ProtocolConfig) {
    Object.keys(protocol).forEach((key) => {
      runInAction(() => {
        (this as any)[key] = (protocol as any)[key];
      });
    });
  }
}

// Individual DoseSchedule store class
export class DoseSchedule {
  // Config data
  dose_canskip: boolean;
  dose_cumulative: string | null;
  dose_discrete: string | null;
  dose_number: string;
  dose_time_min: string;
  dose_xaxis_label: string;

  // Test data
  response: string | null = null;
  result: string | null = null;
  doseid: number | null = null;

  constructor(
    public bhrStore: BhrProcedureStore,
    config: DoseScheduleItem
  ) {
    this.dose_canskip = config.dose_canskip;
    this.dose_cumulative = config.dose_cumulative;
    this.dose_discrete = config.dose_discrete;
    this.dose_number = config.dose_number;
    this.dose_time_min = config.dose_time_min;
    this.dose_xaxis_label = config.dose_xaxis_label;
    this.doseid = config.doseid;

    makeAutoObservable(this);
  }

  setTestData(testData: ProvTestData) {
    this.response = testData.response;
    this.result = testData.result;
    // Note: doseid should already match from config, but verify if needed
    if (this.doseid !== testData.doseid) {
      console.warn(`DoseSchedule doseid mismatch: config=${this.doseid}, testdata=${testData.doseid}`);
    }
  }

  setResult(val: number) {
    this.result = val.toString();
    this.calcResponse();
  }

  get resultValue(): number | null {
    return this.result ? parseFloat(this.result) : null;
  }

  get responseValue(): number | null {
    return this.response ? parseFloat(this.response) : null;
  }

  calcResponse() {
    const currentResult = this.resultValue;
    const refResult = this.bhrStore.doseSchedule.referenceDose?.resultValue;
    if (!currentResult || !refResult) return undefined;

    this.response = ((100 * (currentResult)) / (refResult)).toFixed(1);
  }
}

export class DoseScheduleStore {
  schedules: DoseSchedule[] = [];

  constructor(public bhrStore: BhrProcedureStore) {
    makeAutoObservable(this);
  }

  setSchedules(scheduleItems: DoseScheduleItem[]) {
    this.schedules = scheduleItems
      .toSorted((a, b) => +a.dose_number - +b.dose_number)
      .map((item) => new DoseSchedule(this.bhrStore, item));
  }

  loadTestData(testDataArray: ProvTestData[]) {
    // Create a map of doseid to test data for quick lookup
    const testDataMap = new Map<number, ProvTestData>();
    testDataArray.forEach((testData) => {
      testDataMap.set(testData.doseid, testData);
    });

    // Match test data with dose schedules by doseid
    this.schedules.forEach((dose) => {
      const matchingTestData = testDataMap.get(dose.doseid || -1);
      if (matchingTestData) {
        dose.setTestData(matchingTestData);
      }
    });
  }

  getDoseByNumber(doseNumber: string): DoseSchedule | undefined {
    return this.schedules.find((schedule) => schedule.dose_number === doseNumber);
  }

  getDoseByDoseId(doseid: number): DoseSchedule | undefined {
    return this.schedules.find((schedule) => schedule.doseid === doseid);
  }

  get baselineDose(): DoseSchedule | undefined {
    return this.getDoseByNumber('-1') || this.getDoseByNumber('−1');
  }

  get controlDose(): DoseSchedule | undefined {
    return this.getDoseByNumber('0');
  }

  get postBdDose(): DoseSchedule | undefined {
    return this.getDoseByNumber('100');
  }

  get regularDoses(): DoseSchedule[] {
    return this.schedules.filter((schedule) => !['−1', '-1', '0', '100'].includes(schedule.dose_number));
  }

  get referenceDose(): DoseSchedule | undefined {
    if (this.bhrStore.protocol.p_method_reference === 'Control') {
      return this.controlDose;
    } else if (this.bhrStore.protocol.p_method_reference === 'Baseline') {
      return this.baselineDose;
    }
  }
}

export class ProcedureTestStore {
  name: string;
  parameterValues: ParameterValue[] = [];

  constructor(
    name: string,
    public bhrStore: BhrProcedureStore
  ) {
    this.name = name;
    makeAutoObservable(this);
  }

  addParameterValue(paramValue: ParameterValue) {
    this.parameterValues.push(paramValue);
  }

  getParameterValue(paramName: string): ParameterValue | undefined {
    return this.parameterValues.find((pv) => pv.parameter?.description === paramName);
  }

  fromConfig(testConfig: TestConfig) {
    // Clear existing parameter values
    this.parameterValues = [];

    Object.entries(testConfig).forEach(([, config]) => {
      // Create or get parameter from global store
      const parameter = Parameter.getOrCreate({
        description: config.param.description,
        testid: config.param.testid,
        longname: config.param.longname,
        units: config.param.units,
        units_si: config.param.units_si,
        units_convert_trad_to_si: config.param.units_convert_trad_to_si,
        decimalplaces: config.param.decimalplaces,
        decimalplaces_si: config.param.decimalplaces_si,
      });

      // Map ref_type string to RefType enum
      let refType: RefType;
      switch (config.ref_type.toLowerCase()) {
        case 'lln':
          refType = RefType.LLN;
          break;
        case 'uln':
          refType = RefType.ULN;
          break;
        case 'range':
          refType = RefType.RANGE;
          break;
        default:
          refType = RefType.NO_REF;
      }

      // Create parameter value
      const paramValue = new ParameterValue(
        parameter,
        new ParameterContext(), // Empty context for now
        refType,
        this.bhrStore,
        config.db_field
      );

      this.addParameterValue(paramValue);
    });
  }
}

export class BhrProcedureStore implements DisposableStoreWithUpsert, PredMetadataStore {
  // Basic procedure data
  provid: number | null = null;
  protocolid: number | null = null;
  sessionid: number | null = null;
  patientid: number | null = null;

  // Procedure fields
  bdstatus: string | null = null;
  lab: string | null = null;
  lastupdatedby_prov: string | null = null;
  pd: string | null = null;
  scientist: string | null = null;
  testtype: string | null = null;
  device_info: string | null = null;
  report_amendednotes: string | null = null;
  report_text: string | null = null;
  technicalnotes: string | null = null;
  testtime: string | null = null;

  // Report status fields
  report_amendedby: string | null = null;
  report_authorisedby: string | null = null;
  report_reportedby: string | null = null;
  report_status: string | null = null;
  report_verifiedby: string | null = null;
  report_amendeddate: string | null = null;
  report_authoriseddate: string | null = null;
  report_reporteddate: string | null = null;
  report_verifieddate: string | null = null;

  // Stores
  protocol: ProtocolStore;
  doseSchedule: DoseScheduleStore;
  tests: Map<string, ProcedureTestStore> = new Map();

  // Loading states
  loading: boolean = false;
  configLoading: boolean = false;

  // Required for PredMetadataStore interface
  predMetadata: any = {};

  constructor() {
    makeAutoObservable(this);
    this.protocol = new ProtocolStore();
    this.doseSchedule = new DoseScheduleStore(this);
  }

  setProperty<K extends keyof BhrProcedureStore>(key: K, value: BhrProcedureStore[K]) {
    this[key] = value;
  }

  // Load BHR config from API
  async loadConfig() {
    if (!this.protocolid) return;

    this.setProperty('configLoading', true);
    try {
      const response = await axiosInstance.get<BhrConfig>(`/api/bhr/${this.protocolid}/config`);

      // Load protocol
      this.protocol.fromConfig(response.data.protocol);

      // Load dose schedule
      this.doseSchedule.setSchedules(response.data.dose_schedule);

      // Load tests
      this.tests.clear();
      Object.entries(response.data.tests).forEach(([testName, testConfig]) => {
        const testStore = new ProcedureTestStore(testName, this);
        testStore.fromConfig(testConfig);
        this.tests.set(testName, testStore);
      });
    } finally {
      this.setProperty('configLoading', false);
    }
  }

  // Populate store from GraphQL query result
  fromQueryResult(data: QueryResultType<typeof GET_PROV_TEST>['prov_test'][number]) {
    // Basic fields
    this.setProperty('provid', data.provid);
    this.setProperty('protocolid', data.protocolid);
    this.setProperty('sessionid', data.sessionid);
    this.setProperty('patientid', data.patientid);
    this.setProperty('bdstatus', data.bdstatus);
    this.setProperty('lab', data.lab);
    this.setProperty('lastupdatedby_prov', data.lastupdatedby_prov);
    this.setProperty('pd', data.pd);
    this.setProperty('scientist', data.scientist);
    this.setProperty('testtype', data.testtype);
    this.setProperty('device_info', data.device_info);
    this.setProperty('report_amendednotes', data.report_amendednotes);
    this.setProperty('report_text', data.report_text);
    this.setProperty('technicalnotes', data.technicalnotes);
    this.setProperty('testtime', data.testtime);

    // Report status fields
    this.setProperty('report_amendedby', data.report_amendedby);
    this.setProperty('report_authorisedby', data.report_authorisedby);
    this.setProperty('report_reportedby', data.report_reportedby);
    this.setProperty('report_status', data.report_status);
    this.setProperty('report_verifiedby', data.report_verifiedby);
    this.setProperty('report_amendeddate', data.report_amendeddate);
    this.setProperty('report_authoriseddate', data.report_authoriseddate);
    this.setProperty('report_reporteddate', data.report_reporteddate);
    this.setProperty('report_verifieddate', data.report_verifieddate);

    // Load test data into dose schedules
    if (data.prov_testdata && data.prov_testdata.length > 0) {
      this.doseSchedule.loadTestData(data.prov_testdata as ProvTestData[]);
    }

    // Set spirometry values if they exist
    const spirometryTest = this.tests.get('Spirometry');
    if (spirometryTest) {
      if (data.r_bl_fev1) {
        spirometryTest.getParameterValue('FEV1')?.setResult(parseFloat(data.r_bl_fev1));
        this.doseSchedule.baselineDose?.setResult(parseFloat(data.r_bl_fev1));
      }
      if (data.r_bl_fvc) {
        spirometryTest.getParameterValue('FVC')?.setResult(parseFloat(data.r_bl_fvc));
      }
      if (data.r_bl_vc) {
        spirometryTest.getParameterValue('VC')?.setResult(parseFloat(data.r_bl_vc));
      }
      if (data.r_bl_fef2575) {
        spirometryTest.getParameterValue('FEF2575')?.setResult(parseFloat(data.r_bl_fef2575));
      }
      if (data.r_bl_pef) {
        spirometryTest.getParameterValue('PEF')?.setResult(parseFloat(data.r_bl_pef));
      }
      if (data.r_bl_fer) {
        spirometryTest.getParameterValue('FER')?.setResult(parseFloat(data.r_bl_fer));
      }
    }
  }

  // Get test by name
  getTest(testName: string): ProcedureTestStore | undefined {
    return this.tests.get(testName);
  }

  // Required for DisposableStoreWithUpsert interface
  async upsertDB(field?: string): Promise<void> {
    // Implementation would depend on the specific mutation
    // This is a placeholder for the required interface
    console.log('upsertDB called for field:', field);
  }

  get spirometryTest(): ProcedureTestStore | undefined {
    return this.getTest('Spirometry');
  }

  get isLoading(): boolean {
    return this.loading || this.configLoading;
  }

  private isNumeric(value: string | null): boolean {
    if (!value) return false;
    try {
      parseFloat(value);
      return true;
    } catch {
      return false;
    }
  }

  private interpolateDose(
    dose1: number,
    dose2: number,
    response1: number,
    response2: number,
    targetLevel: number
  ): number {
    const a = Math.log10(dose1);
    const c = Math.log10(dose2);
    const b = response1;
    const m = (b - response2) / (a - c);

    return Math.pow(10, (targetLevel - (b - m * a)) / m);
  }

  get pdx(): string {
    const protocol = this.protocol;
    if (!protocol) return '';

    // Get threshold values
    const threshFall = parseFloat(protocol.pd_threshold || '0');
    if (threshFall === 0) return '';

    // Determine threshold level based on method reference
    const methodReference = protocol.p_method_reference || '';

    // Get test data ordered by dose number
    const testData = [...this.doseSchedule.schedules].sort(
      (a, b) => parseFloat(a.dose_number || '0') - parseFloat(b.dose_number || '0')
    );

    const refDose = this.doseSchedule.referenceDose;
    if (!refDose) return '';

    let threshLevel = (refDose?.responseValue ?? 0) - threshFall;

    // Filter valid doses (exclude baseline, post-BD, and skipped doses)
    const validDoses: number[] = [];
    const validResponses: number[] = [];

    for (const td of testData) {
      if (!['−1', '-1', '0', '100'].includes(td.dose_number) && (td.responseValue ?? 0) > 0) {
        validResponses.push(td.responseValue!);

        // Use cumulative or discrete dose based on protocol
        const doseEffect = protocol.p_dose_effect || '';
        if (doseEffect === 'Cumulative') {
          if (this.isNumeric(td.dose_cumulative)) {
            validDoses.push(parseFloat(td.dose_cumulative!));
          } else {
            return '';
          }
        } else if (doseEffect === 'Discrete') {
          if (this.isNumeric(td.dose_discrete)) {
            validDoses.push(parseFloat(td.dose_discrete!));
          } else {
            return '';
          }
        } else {
          return '';
        }
      }
    }

    if (validDoses.length < 2) return '';

    // Find last valid dose
    let doseLastIdx = validDoses.length - 1;
    while (doseLastIdx > 0 && !validResponses[doseLastIdx]) {
      doseLastIdx--;
    }

    if (doseLastIdx <= 0) return '';

    // Find doses that bracket the defined fall
    let fallAchieved = false;
    let doseAfterIdx = -1;

    for (let i = 0; i < validResponses.length; i++) {
      if (validResponses[i] === threshLevel) {
        return `${validDoses[i]} ${protocol.p_agent_units || ''}`;
      } else if (validResponses[i] < threshLevel) {
        fallAchieved = true;
        doseAfterIdx = i;
        break;
      }
    }

    const agentUnits = protocol.p_agent_units || '';
    const agent = protocol.p_agent || '';

    // If defined fall not achieved
    if (!fallAchieved) {
      // For Mannitol, check 10% change rule
      if (agent.toLowerCase() === 'mannitol') {
        if (doseLastIdx > 0 && validResponses[doseLastIdx - 1] - validResponses[doseLastIdx] >= 10) {
          // Extrapolate PD to threshold fall
          const extrapolatedDose = this.interpolateDose(
            validDoses[doseLastIdx],
            validDoses[doseLastIdx - 1],
            validResponses[doseLastIdx],
            validResponses[doseLastIdx - 1],
            threshLevel
          );
          const decimalPlaces = protocol.pd_decimalplaces || 1;
          return `${extrapolatedDose.toFixed(decimalPlaces)} ${agentUnits} (10% rule invoked)`;
        } else {
          return `> ${validDoses[doseLastIdx]} ${agentUnits}`;
        }
      } else {
        return `> ${validDoses[doseLastIdx]} ${agentUnits}`;
      }
    } else {
      // Check if fall occurred after first dose
      if (methodReference === 'Control' && doseAfterIdx === 0) {
        return `< ${validDoses[doseAfterIdx]} ${agentUnits}`;
      } else if (methodReference === 'Baseline' && doseAfterIdx === 0) {
        return `Response to control dose ${agentUnits}`;
      }

      // Interpolate PD
      if (doseAfterIdx > 0) {
        const interpolatedDose = this.interpolateDose(
          validDoses[doseAfterIdx],
          validDoses[doseAfterIdx - 1],
          validResponses[doseAfterIdx],
          validResponses[doseAfterIdx - 1],
          threshLevel
        );
        const decimalPlaces = protocol.pd_decimalplaces || 1;
        return `${interpolatedDose.toFixed(decimalPlaces)} ${agentUnits}`;
      }
    }

    return '';
  }
}

export interface BhrProcedureStore extends DisposableStoreMixin {}

applyMixins(BhrProcedureStore, [DisposableStoreMixin]);
