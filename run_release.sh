#!/bin/bash
set -xeuo pipefail

# Get the absolute path of the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

GRAPHQL_PID=""

# --- Cleanup Function ---
# This function will be called on script exit to ensure the GraphQL engine is stopped.
cleanup_graphql_engine() {
  "${SCRIPT_DIR}/wait_for_hasura_to_die.sh" "$GRAPHQL_PID"
}

# --- Trap Setup ---
# Call cleanup_graphql_engine when the script exits for any reason.
trap cleanup_graphql_engine EXIT

export RUN_HASURA_BACKGROUND=true
source "${SCRIPT_DIR}/run_hasura.sh"

"${SCRIPT_DIR}/run_update_hasura.sh"
