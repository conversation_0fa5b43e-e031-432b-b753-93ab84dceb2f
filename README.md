# Rezibase

## Prerequisites

To get started, ensure you have the following installed:
- [Docker Compose](https://docs.docker.com/compose/gettingstarted/)
- [pnpm](https://pnpm.io/installation)

## FE Setup Instructions

Follow these steps to set up the project:

1. Navigate to the frontend directory:
   ```
   cd FE
   ```
2. Install the dependencies:
   ```
   pnpm i
   ```
3. Build the frontend:
   ```
   pnpm run build
   ```
4. Return to the root directory:
   ```
   cd ..
   ```
5. Create a `.env` file in the root directory:
    - Copy the contents of `.env.example` into a new `.env` file and adjust the values as needed.
6. Start the application using Docker Compose:
   ```
   docker compose up
   ```


## Load Data into Local Database

### Steps

1. **Start all containers**
   ```bash
   docker  compose up
   ```

2. **Add Dump File**  
   Place the dump file (e.g., `rezibase.dump`) in `postgres_data/`.

3. **Restore Database**
   ```bash
   docker exec -it rezibase-app-db-1 pg_restore -U rezibase -d rezibase -Fc /data/<DUMP_FILE_NAME> --no-owner --single-transaction --clean --if-exists
   ```
   Replace `<DUMP_FILE_NAME>` with your dump file name.

### Troubleshooting
- **Container not found**: Verify `rezibase-app-db-1` is running (`docker ps`).
- **File not found**: Ensure dump file is in `postgres_data/` and name matches.
- **Permissions**: Check file readability (`chmod 644`) and user access.

### Notes
- `postgres_data/` maps to `/data` in the container.
- `--clean` drops existing objects; back up first.
- Stop database with `docker-compose down`.


## # rezibase-app-binaries

## How to upgrade Hasura

We use Hasura as a graphQL server.

Hasura doesn't release versioned statically-linked binaries (or installers), for their graph engine (just the CLI). So we copy the graph-engine out of the docker file, identify the dependencies, and save the binary file into the binary's repository.

### Steps
0. Check out the binaries repository.

1. Update the DockerFile to refer to the newer version of Hasura.

2. Copy out the binary:
```
docker create --name hasura-extractor hasura 
docker cp hasura-extractor:/usr/bin/graphql-engine ./graphql-engine
docker rm hasura-extractor
```

If the graphql-engine isn't extracted, log in and find it:
```
docker run -it --rm --entrypoint /bin/sh hasura
```

3. Try to run the graph-engine in Ubuntu. Personally, I just try to run it in WSL on my windows PC. Then identify any missing dependencies, then add them into the Aptfile in the root directory of the rezibase-app.

The graph-engine is dynamically-linked, so run it and try to find the dependencies.

As of v2.47.0 they're:

```
sudo apt-get install libpcre3
sudo apt-get install unixodbc
sudo apt-get install libpq5
```

4. Get the CLI with the corresponding version from GitHub.
https://github.com/hasura/graphql-engine/releases

5. Save both the engine and CLI into this repository following the naming convention.

6. Update start-engine.sh and hasura.sh of rezibase-app to refer to the new files.

7. Save it into this repository.


### How build works.

The build process is a bit complicated. Here we go.

When we do a build we:

1. Connect to a temporary database
2. Clean it out
3. Restore a backup to it.
4. Upgrade it with flask upgrade
5. Connect a Hasura engine to it.
6. Upgrade the hasura instance
7. Compile our software
8. Connect to the hasura engine to get the types for the API.
9. Shut down the temporary hasura instance.
10.Some stuff to note:

The Hasura binaries are taken from our repository
There's a database in the same repository that we restore the backup from.
When we release we:

1. Upgrade the database with flask upgrade
2. Upgrade the Hasura engine.


### How to create a new application.
This is a partial list of steps to create a new application in Heroku.
It's best to run each line one-by-one as you run them.

```
set source=rezibase-web-staging
set target=rezibase-cis-uat

REM heroku addons:create heroku-postgresql:essential-0 --as BUILD_DATABASE --app %target%

for %%v in (APP_TYPE AWS_ACCESS_KEY AWS_REGION_NAME GITHUB_BINARY_REPO_TOKEN HASURA_GRAPHQL_ADMIN_SECRET JWT_SECRET_KEY) do (
     for /f "delims=" %%i in ('heroku config:get %%v --app %source%') do heroku config:set %%v=%%i --app %target%
 )

REM List the buildpacks, then udpate the next lines...
REM heroku buildpacks --app %source%

REM heroku buildpacks:add heroku-community/apt --index 1 --app %target%
REM heroku buildpacks:add https://github.com/clinibase/heroku-buildpack-traefik --index 2 --app %target%
REM heroku buildpacks:add heroku/python --index 3  --app %target%
REM heroku buildpacks:add heroku/nodejs --index 4 --app %target%

REM In the UI enable automatic deploys
REM In the UI Deploy main branch

REM By default the release will be 500MB and will fail lots.
REM heroku ps:scale web=2:standard-2x -app %target%
REM heroku ps:type release=standard-2x --app %target%

REM heroku pg:copy %source%::DATABASE_URL DATABASE_URL --app %target%

REM heroku pg:backups:schedule --at '02:00 Australia/Melbourne' --app %target%
```