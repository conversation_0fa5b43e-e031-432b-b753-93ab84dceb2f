CREATE TABLE TEMP_list_units
(
  description VARCHAR(50),
  shortname   VA<PERSON>HA<PERSON>(20),
  enabled     BOOLEAN,
  site_id     INT
);

INSERT INTO TEMP_list_units (description, shortname, enabled, site_id)
SELECT description,
       shortname,
       enabled,
       site_id
FROM list_units;

ALTER TABLE procedure
  DROP CONSTRAINT procedure_unit_id_fkey;

DROP TABLE list_units;

CREATE TABLE IF NOT EXISTS list_units
(
  id          SERIAL  NOT NULL,
  description character varying(50),
  shortname   character varying(20),
  enabled     boolean NOT NULL DEFAULT true,
  site_id     integer NOT NULL DEFAULT 1,
  CONSTRAINT pk_list_units PRIMARY KEY (id),
  CONSTRAINT list_units_site_id_fkey FOREIGN KEY (site_id)
    REFERENCES public.site (id)
);

INSERT INTO list_units (description, shortname, enabled, site_id)
SELECT description,
       shortname,
       enabled,
       site_id
FROM TEMP_list_units;

ALTER TABLE procedure
  ADD CONSTRAINT procedure_unit_id_fkey FOREIGN KEY (unit_id)
    REFERENCES list_units (id);

DROP TABLE TEMP_list_units;

