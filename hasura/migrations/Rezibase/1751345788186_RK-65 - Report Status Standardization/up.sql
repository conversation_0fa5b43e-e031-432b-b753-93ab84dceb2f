CREATE TABLE customer
(
	customer_name VARCHAR(50) NOT NULL,
	CONSTRAINT PK_customer PRIMARY KEY (customer_name)
);


CREATE TABLE report_status_type
(
	report_status_type_id INT NOT NULL,
	report_status_type VARCHAR(32) NOT NULL,
	CONSTRAINT PK_report_status_type PRIMARY KEY (report_status_type_id)
);

ALTER TABLE list_reportstatuses
ADD report_status_type_id INT NULL;

ALTER TABLE list_reportstatuses
ADD CONSTRAINT FK_list_reportstatuses_report_status_type FOREIGN KEY (report_status_type_id) REFERENCES report_status_type (report_status_type_id);


INSERT INTO report_status_type(report_status_type_id, report_status_type)
VALUES
(0, 'Unreported'),
(1, 'Completed'),
(2, 'Amended'),
(3, 'Discussion'),
(4, 'Preliminary'),
(5, 'Cancelled');

UPDATE list_reportstatuses
SET report_status_type_id = 0
WHERE description IN ('Unreported');

UPDATE list_reportstatuses
SET report_status_type_id = 1
WHERE description IN ('Completed', 'Completed and amended');

UPDATE list_reportstatuses
SET report_status_type_id = 2
WHERE description IN ('Completed and amended');

UPDATE list_reportstatuses
SET report_status_type_id = 3
WHERE description IN ('Reported not verified', 'For discussion', 'Verified not printed');

UPDATE list_reportstatuses
SET report_status_type_id = 4
WHERE description IN ('Interim');

ALTER TABLE list_reportstatuses
ALTER COLUMN report_status_type_id SET NOT NULL;


----------------------------------------------------------------------------------------------------------------------------------------

ALTER TABLE rft_routine
ADD report_status_id INT NULL;

ALTER TABLE rft_routine
ADD CONSTRAINT FK_rft_routine_report_status FOREIGN KEY (report_status_id) REFERENCES list_reportstatuses (statusid);

----------------------------------------------------------------------------------------------------------------------------------------

UPDATE rft_routine rft
SET report_status_id = rs.statusid
FROM list_reportstatuses rs
WHERE 1=1
AND rft.report_status = rs.description
AND rft.report_status IS NOT NULL;

