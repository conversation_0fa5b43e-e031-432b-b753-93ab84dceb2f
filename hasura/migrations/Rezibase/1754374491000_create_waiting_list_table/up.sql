-- Create table if it doesn't exist
CREATE TABLE IF NOT EXISTS waiting_list
(
  id                         SERIAL PRIMARY KEY,
  urgency                    VARCHAR(50)              DEFAULT 'standard',
  procedure_ids              INTEGER[]                DEFAULT NULL,
  patientid                  INTEGER NOT NULL,
  notes                      TEXT                     DEFAULT NULL,
  referral_date              DATE                     DEFAULT NULL,
  provisional_procedure_date DATE                     DEFAULT NULL,
  created_at                 TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at                 TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  requested_timeframe        VARCHAR(100)             DEFAULT NULL,
  admission_type             VARCHAR(100)             DEFAULT NULL,
  discharge_location         VARCHAR(100)             DEFAULT NULL,
  unit_id                    INTEGER NOT NULL,
  study_request_item_id      INTEGER                  DEFAULT NULL,
  CONSTRAINT fk_waiting_list_patient
    FOREIGN KEY (patientid) REFERENCES pas_pt (patientid)
      ON DELETE CASCADE,
  CONSTRAINT fk_waiting_list_units
    FOREIGN KEY (unit_id) REFERENCES list_units (id)
      ON DELETE CASCADE,
  CONSTRAINT fk_waiting_list_study_request
    FOREIGN KEY (study_request_item_id) REFERENCES study_request_item (id)
      ON DELETE SET NULL
);

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_waiting_list_patientid ON waiting_list (patientid);
CREATE INDEX IF NOT EXISTS idx_waiting_list_unit_id ON waiting_list (unit_id);
CREATE INDEX IF NOT EXISTS idx_waiting_list_study_request_item_id ON waiting_list (study_request_item_id);
CREATE INDEX IF NOT EXISTS idx_waiting_list_urgency ON waiting_list (urgency);
CREATE INDEX IF NOT EXISTS idx_waiting_list_provisional_date ON waiting_list (provisional_procedure_date);
CREATE INDEX IF NOT EXISTS idx_waiting_list_referral_date ON waiting_list (referral_date);
CREATE INDEX IF NOT EXISTS idx_waiting_list_created_at ON waiting_list (created_at);