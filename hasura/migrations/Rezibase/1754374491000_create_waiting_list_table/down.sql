-- Migration Rollback: Drop waiting_list table
-- File: down.sql

-- Drop indexes (they will be dropped automatically with the table, but explicit for clarity)
DROP INDEX IF EXISTS idx_waiting_list_patientid;
DROP INDEX IF EXISTS idx_waiting_list_unit_id;
DROP INDEX IF EXISTS idx_waiting_list_study_request_item_id;
DROP INDEX IF EXISTS idx_waiting_list_urgency;
DROP INDEX IF EXISTS idx_waiting_list_provisional_date;
DROP INDEX IF EXISTS idx_waiting_list_created_at;

-- Drop the table
DROP TABLE IF EXISTS waiting_list;