CREATE TABLE "public"."activity_logs"
(
    "id"          SERIAL PRIMARY KEY,
    "user_id"     INTEGER,
    "action_type" TEXT                      NOT NULL,
    "table_name"  TEXT                      NOT NULL,
    "record_id"   INTEGER,
    "details"     JSONB,
    "ip_address"  TEXT,
    "created_at"  TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Create index on user_id for faster lookups
CREATE INDEX "activity_logs_user_id_idx" ON "public"."activity_logs" ("user_id");

-- Create index on table_name for filtering
CREATE INDEX "activity_logs_table_name_idx" ON "public"."activity_logs" ("table_name");

-- Create index on created_at for timeline queries
CREATE INDEX "activity_logs_created_at_idx" ON "public"."activity_logs" ("created_at");
