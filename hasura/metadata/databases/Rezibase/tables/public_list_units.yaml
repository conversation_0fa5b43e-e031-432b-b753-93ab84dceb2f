table:
  name: list_units
  schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - description
        - enabled
        - id
        - shortname
        - site_id
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
    comment: ""
event_triggers:
  - name: list_units
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
