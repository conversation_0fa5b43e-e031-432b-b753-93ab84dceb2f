table:
  name: prov_test
  schema: public
object_relationships:
  - name: pas_pt
    using:
      foreign_key_constraint_on: patientid
  - name: r_session
    using:
      foreign_key_constraint_on: sessionid
select_permissions:
  - role: user
    permission:
      columns:
        - flowvolloop
        - bdstatus
        - lab
        - lastupdatedby_prov
        - p_agent
        - p_agent_units
        - pd
        - pd_decimalplaces
        - p_description
        - pd_method
        - p_dose_effect
        - pd_threshold
        - plot_title
        - plot_xscaling_type
        - plot_xtitle
        - plot_ymax
        - plot_ymin
        - plot_ystep
        - p_method_reference
        - p_parameter
        - p_parameter_response
        - p_parameter_units
        - p_post_drug
        - p_title
        - r_bl_fef2575
        - r_bl_fer
        - r_bl_fev1
        - r_bl_fvc
        - r_bl_pef
        - r_bl_vc
        - report_amendedby
        - report_authorisedby
        - report_reportedby
        - report_status
        - report_verifiedby
        - scientist
        - testtype
        - report_amendeddate
        - report_authoriseddate
        - report_reporteddate
        - report_verifieddate
        - patientid
        - protocolid
        - provid
        - sessionid
        - device_info
        - report_amendednotes
        - report_text
        - technicalnotes
        - lastupdated_prov
        - testtime
      filter: {}
      allow_aggregations: true
    comment: ""
event_triggers:
  - name: prov_test
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
