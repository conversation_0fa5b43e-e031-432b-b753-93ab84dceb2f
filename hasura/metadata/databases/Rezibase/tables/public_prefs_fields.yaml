table:
  name: prefs_fields
  schema: public
array_relationships:
  - name: prefs_fielditems
    using:
      foreign_key_constraint_on:
        column: field_id
        table:
          name: prefs_fielditems
          schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - fieldname
        - default_fielditem_id
        - field_id
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
    comment: ""
event_triggers:
  - name: prefs_fields
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
