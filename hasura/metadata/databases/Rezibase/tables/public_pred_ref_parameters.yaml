table:
  name: pred_ref_parameters
  schema: public
object_relationships:
  - name: pred_ref_test
    using:
      foreign_key_constraint_on: testid
select_permissions:
  - role: user
    permission:
      columns:
        - description
        - longname
        - units
        - units_si
        - units_convert_trad_to_si
        - valid_high
        - valid_high_si
        - valid_low
        - valid_low_si
        - decimalplaces
        - decimalplaces_si
        - id
        - parameterid
        - reporttypeid_notused
        - testid
      filter: {}
    comment: ""
event_triggers:
  - name: pred_ref_parameters
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
