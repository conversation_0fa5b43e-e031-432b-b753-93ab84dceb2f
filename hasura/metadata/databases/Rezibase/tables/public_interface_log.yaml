table:
  name: interface_log
  schema: public
object_relationships:
  - name: interface
    using:
      foreign_key_constraint_on: interface_id
  - name: patient
    using:
      foreign_key_constraint_on: patient_id
insert_permissions:
  - role: user
    permission:
      check: {}
      columns:
        - event_ack_code
        - event_date_time
        - event_msg
        - event_msg_response
        - event_success
        - failure_reason
        - interface_id
        - message_received
        - message_type
        - msg_ctrl_id
        - patient_id
        - trigger_event
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - event_success
        - event_ack_code
        - failure_reason
        - message_type
        - msg_ctrl_id
        - trigger_event
        - interface_id
        - interface_log_id
        - patient_id
        - event_msg
        - event_msg_response
        - event_date_time
        - message_received
      filter: {}
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - event_ack_code
        - event_date_time
        - event_msg
        - event_msg_response
        - event_success
        - failure_reason
        - interface_id
        - message_received
        - message_type
        - msg_ctrl_id
        - patient_id
        - trigger_event
      filter: {}
      check: null
    comment: ""
event_triggers:
  - name: interface_log
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
