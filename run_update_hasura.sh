#!/bin/bash
set -xeuo pipefail

# Get the absolute path of the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

cd "${SCRIPT_DIR}/hasura"

export HASURA_DB_NAME="${HASURA_DB_NAME:-Rezibase}"
ENDPOINT="http://localhost:${HASURA_PORT:-3020}"

# Helper for running hasura-cli commands.
function hasura() {
    ./hasura-cli "$@" --endpoint "${ENDPOINT}"
}

#This should fail on error.
hasura migrate apply --database-name "${HASURA_DB_NAME}"

#From now, we want to be careful about failing, because if we exit it'll
#leave hasura and postgres in an inconsistent state. We can only fail
#from here in the testing environment and in build.
if [[ "${FAIL_ON_HASURA_INCONSISTENCY:-false}" == "false" ]]; then
	echo "No failing on non-zero exit"
	set +e
fi

# In the Hasura docs, it's 'metadata apply' then 'migrate apply'.
# I think that's wrong though, and we need to update the database
# i.e. migrate first, then do the meta data afterwards.
# https://hasura.io/docs/2.0/migrations-metadata-seeds/manage-migrations/

hasura migrate status --database-name "${HASURA_DB_NAME}"

# Apply new metadata
hasura metadata reload
hasura metadata apply
hasura metadata reload

hasura metadata inconsistency list
hasura metadata inconsistency status

echo "Done updating Hasura"
