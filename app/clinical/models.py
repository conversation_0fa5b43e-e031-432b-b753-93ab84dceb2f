from typing import Union, TYPE_CHECKING

from faker import Faker
from sqlalchemy.orm import Mapped

from app import db

if TYPE_CHECKING:
    from app.pdf_convert.models import PasPt

fake = Faker()


class PredRefParameters(db.Model):
    __tablename__ = 'pred_ref_parameters'
    __table_args__ = {'extend_existing': True}

    id = db.Column('id', db.Integer, primary_key=True)
    parameter_id = db.Column('parameterid', db.Integer)
    description = db.Column('description', db.String(255))
    testid = db.Column('testid', db.Integer)
    reporttype_id_notused = db.Column('reporttypeid_notused', db.Integer)
    longname = db.Column('longname', db.String(255))
    units = db.Column('units', db.String(255))
    units_si = db.Column('units_si', db.String(255))
    units_convert_trad_to_si = db.Column('units_convert_trad_to_si', db.Float)
    decimalplaces = db.Column('decimalplaces', db.Integer)
    valid_low = db.Column('valid_low', db.Float)
    valid_high = db.Column('valid_high', db.Float)
    decimalplaces_si = db.Column('decimalplaces_si', db.Integer)
    valid_low_si = db.Column('valid_low_si', db.Float)
    valid_high_si = db.Column('valid_high_si', db.Float)

    def format_value(self, val: Union[float, int], traditional=True):
        if (type(val) is float or type(val) is int):
            if traditional and self.decimalplaces:
                return '{:.{}f}'.format(val, self.decimalplaces)
            elif not traditional and self.decimalplaces_si:
                return '{:.{}f}'.format(val, self.decimalplaces_si)

        return '-'


class RSession(db.Model):
    __tablename__ = 'r_sessions'

    sessionid = db.Column(db.Integer, primary_key=True)
    patientid = db.Column(db.Integer, db.ForeignKey('pas_pt.patientid'), nullable=True)

    testdate = db.Column(db.Date)
    lab = db.Column(db.String)
    height = db.Column(db.String)
    weight = db.Column(db.String)

    req_name = db.Column(db.String)
    req_address = db.Column(db.String)
    req_providernumber = db.Column(db.String)
    req_healthservice_text = db.Column(db.String)
    req_healthservice_code = db.Column(db.String)

    req_date = db.Column(db.Date)
    req_time = db.Column(db.Time)
    req_phone = db.Column(db.String)
    req_fax = db.Column(db.String)
    req_email = db.Column(db.String)
    req_clinicalnotes = db.Column(db.Text)

    smoke_hx = db.Column(db.String)
    smoke_cigsperday = db.Column(db.String)
    smoke_yearssmoked = db.Column(db.String)
    smoke_packyears = db.Column(db.String)
    smoke_last = db.Column(db.String)
    diagnosticcategory = db.Column(db.String)
    pred_sourceids = db.Column(db.Text)

    admissionstatus = db.Column(db.String)
    report_copyto = db.Column(db.String)
    report_copyto_2 = db.Column(db.String)

    billing_billingmo = db.Column(db.String)
    billing_billingmoproviderno = db.Column(db.String)

    lastupdated_session = db.Column(db.TIMESTAMP)
    lastupdatedby_session = db.Column(db.String)

    # --- Relationships ---
    patient: Mapped['PasPt'] = db.relationship("PasPt", backref="sessions")

    @property
    def age_at_test(self):
        return self.patient.age_at_date(self.testdate)

    @property
    def bmi(self):
        if not self.weight or not self.height:
            return None

        try:
            weight = float(self.weight)
            height = float(self.height)

            return round((weight / (height ** 2) * 10000), 1)
        except (ValueError, TypeError, ZeroDivisionError):
            return None

    @classmethod
    def create_fake(cls, patientid):
        """Create a fake RSession record based on real-world examples"""

        # Generate realistic data
        testdate = fake.date_between(start_date='-30d')
        req_date = fake.date_between(start_date=testdate, end_date=testdate)

        # Randomly decide if smoking history should be included
        has_smoking_history = fake.boolean(chance_of_getting_true=40)

        session = cls(
            patientid=patientid,
            testdate=testdate,
            height="172",
            weight="75",

            # Requester information
            req_name=f"Dr. {fake.first_name()} {fake.last_name()}" if fake.boolean(chance_of_getting_true=80) else None,
            req_address=fake.street_address() + ", " + fake.city() + " " + fake.state_abbr() + " " + fake.postcode()
            if fake.boolean(
                chance_of_getting_true=60
            ) else None,
            req_providernumber=str(fake.unique.random_number(digits=8)) if fake.boolean(
                chance_of_getting_true=70
            ) else None,
            req_healthservice_text=f"{fake.company()} Hospital",
            req_date=req_date,
            req_time=fake.time_object(),
            req_phone=fake.phone_number() if fake.boolean(chance_of_getting_true=60) else None,
            req_fax=fake.phone_number() if fake.boolean(chance_of_getting_true=20) else None,
            req_email=fake.email() if fake.boolean(chance_of_getting_true=50) else None,
            req_clinicalnotes=fake.text(max_nb_chars=200) if fake.boolean(chance_of_getting_true=70) else None,

            # Smoking history
            smoke_hx=fake.random_element(elements=('Y', 'N')) if has_smoking_history else None,
            smoke_cigsperday=str(fake.random_int(min=1, max=40)) if has_smoking_history and fake.boolean(
                chance_of_getting_true=60
            ) else None,
            smoke_yearssmoked=str(fake.random_int(min=1, max=50)) if has_smoking_history and fake.boolean(
                chance_of_getting_true=60
            ) else None,
            smoke_packyears=str(fake.random_int(min=1, max=100)) if has_smoking_history and fake.boolean(
                chance_of_getting_true=60
            ) else None,
            smoke_last=fake.date_between(start_date='-5y', end_date='today').strftime(
                '%Y-%m-%d'
            ) if has_smoking_history and fake.boolean(chance_of_getting_true=40) else None,

            # Diagnostic and admission information
            diagnosticcategory=fake.random_element(elements=('Respiratory', 'Cardiovascular', None)),
            admissionstatus=fake.random_element(elements=('Outpatient', 'Inpatient')),
            report_copyto=fake.name() if fake.boolean(chance_of_getting_true=30) else None,
            report_copyto_2=fake.name() if fake.boolean(chance_of_getting_true=20) else None,

            # Timestamps
            lastupdated_session=fake.date_time_between(start_date='-1d', end_date='now'),
            lastupdatedby_session=fake.random_element(
                elements=('user123', 'current_user', 'admin', 'doctor1', 'nurse1')
            )
        )

        return session
