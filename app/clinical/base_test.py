from typing import Optional

from app.clinical.types import TestParameter, DbFieldValue, ClinicalModule


class BaseTestMeta(type):
    """Metaclass that collects DbFieldValue and ParameterValue instances"""

    def __new__(mcs, name, bases, namespace, **kwargs):
        # Collect all field instances from the class definition
        fields = {}

        # First, collect fields from base classes
        for base in bases:
            if hasattr(base, '_fields'):
                fields.update(base._fields)

        # Then collect fields from current class
        for key, value in list(namespace.items()):
            if isinstance(value, (DbFieldValue, TestParameter)):
                fields[key] = value

        # Create the class
        cls = super().__new__(mcs, name, bases, namespace)

        # Store fields in class attribute
        cls._fields = fields

        return cls


class BaseTest(metaclass=BaseTestMeta):
    name: Optional[str] = None

    def __init__(self, module: 'ClinicalModule' = None):
        self.clinical_module = module

        if module:
            self.set_clinical_module(module)

    def set_clinical_module(self, clinical_module):
        """Set the clinical module on all fields"""
        self.clinical_module = clinical_module
        for field in self._fields.values():
            field.clinical_module = clinical_module

    def get_parameter_values(self) -> list['TestParameter']:
        """Get a list of all parameter values"""
        return [getattr(self, name) for name, value in self._fields.items() if isinstance(value, TestParameter)]

    def is_empty(self) -> bool:
        """Check if this test has all empty parameter values"""
        parameter_values = self.get_parameter_values()
        for param in parameter_values:
            if param.value is not None and param.value != '' and param.value != 0:
                return False
        return True
