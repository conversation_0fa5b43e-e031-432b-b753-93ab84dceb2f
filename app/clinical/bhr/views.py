from flask import jsonify, abort
from flask_jwt_extended import jwt_required

from app.clinical import clinical_bp
from app.clinical.bhr.bhr_module import BhrModule
from app.clinical.bhr.models import ProvProtocols
from app.clinical.models import PredRefParameters
from flask import render_template, send_file
from tempfile import NamedTemporaryFile
import pdfkit


@clinical_bp.route('/api/bhr/<int:protocol_id>/config', methods=['GET'])
@jwt_required()
def bhr_config(protocol_id: int):
    protocol = ProvProtocols.query.get_or_404(protocol_id)

    if not protocol.enabled:
        abort(404)

    PredRefParameters.query.all()


    return jsonify({
        **BhrModule.get_config(),
        'protocol': {
            'p_title': protocol.p_title,
            'p_description': protocol.p_description,
            'p_parameter': protocol.p_parameter,
            'p_parameter_units': protocol.p_parameter_units,
            'p_parameter_response': protocol.p_parameter_response,
            'p_method_reference': protocol.p_method_reference,
            'p_agent': protocol.p_agent,
            'p_agent_units': protocol.p_agent_units,
            'p_dose_effect': protocol.p_dose_effect,
            'p_menulabel': protocol.p_menulabel,
            'pd_threshold': protocol.pd_threshold,
            'pd_decimalplaces': protocol.pd_decimalplaces,
            'pd_method': protocol.pd_method,
            'p_post_drug': protocol.p_post_drug,
            'plot_xscaling_type': protocol.plot_xscaling_type,
            'plot_xtitle': protocol.plot_xtitle,
            'plot_title': protocol.plot_title,
            'plot_ymin': protocol.plot_ymin,
            'plot_ymax': protocol.plot_ymax,
            'plot_ystep': protocol.plot_ystep,
        },
        'dose_schedule': [
            {
                "dose_time_min": dose_schedule.dose_time_min,
                "dose_canskip": dose_schedule.dose_canskip,
                "dose_number": dose_schedule.dose_number,
                "dose_discrete": dose_schedule.dose_discrete,
                "dose_cumulative": dose_schedule.dose_cumulative,
                "dose_xaxis_label": dose_schedule.dose_xaxis_label,
                "doseid": dose_schedule.doseid
            }
            for dose_schedule in protocol.dose_schedules
        ]
    })


@clinical_bp.route('/api/bhr/<int:prov_id>/report', methods=['POST'])
@jwt_required()
def bhr_report(prov_id: int):
   
    html = render_template('reports/mannitol-report.html')

    with NamedTemporaryFile(suffix='.pdf') as f:
        options = {
            'page-size': 'A4',
            'enable-local-file-access': None,
            'load-error-handling': 'ignore',
            'disable-smart-shrinking': '',
        }

        pdfkit.from_string(html, f.name, options=options)
        return send_file(f.name, as_attachment=True)
