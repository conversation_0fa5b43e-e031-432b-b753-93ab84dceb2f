from flask import render_template, send_file, request, jsonify
from sqlalchemy.orm import joinedload
from flask_jwt_extended import jwt_required

from tempfile import NamedTemporaryFile
import pdfkit
import base64

from app.clinical.blueprint import clinical_bp
from app.clinical.models import PredRefParameters
from app.clinical.rft.models import RftRoutine
from app.clinical.rft.rft_module import RftModule
from app.clinical.rft.zscore_plotter import generate_zscore_plot
from app.auth.token import get_current_site_id
from flask import current_app
from app.site.site_settings import PDF_HEADER_IMG, PDF_HEADER_TEXT, PDF_FILE_NAME
import os


@clinical_bp.route('/api/rft/<int:rft_id>/report', methods=['POST'])
@jwt_required()
def rft_report(rft_id: int):

    site_id = get_current_site_id()
    if not site_id:
        return {
            'pdf_picture': None,
            'rows': []
        }, 400

    default_image_path = os.path.join(current_app.root_path, 'static/assets/media/logos/logo-23.png')
    header_img = PDF_HEADER_IMG.get_for_current_site()

    if header_img and header_img.get('image', None):
        img = header_img.get('image')
        image_data = f"data:{img.get('mimetype')};base64,{img.get('data')}"
    else:
        try:
            with open(default_image_path, 'rb') as f:
                encoded = base64.b64encode(f.read()).decode('utf-8')
                image_data = f"data:image/png;base64,{encoded}"
        except Exception as e:

            image_data = None


    rft_routine = RftRoutine.query.options(
        joinedload(RftRoutine.session),
        joinedload(RftRoutine.patient),
    ).get_or_404(rft_id)
    PredRefParameters.query.all()
    rft_module = RftModule(rft_routine)
    plot_params = [
        {'sp1': rft_module.sp1.fev1, 'sp2': rft_module.sp2.fev1},
        {'sp1': rft_module.sp1.fvc, 'sp2': rft_module.sp2.fvc},
        {'sp1': rft_module.sp1.vc, 'sp2': rft_module.sp2.vc},
        {'sp1': rft_module.sp1.fer, 'sp2': rft_module.sp2.fer},
        rft_module.co_transfer.kco,
        rft_module.co_transfer.va,
        rft_module.lung_volumes.tlc,
        rft_module.lung_volumes.frc,
        rft_module.lung_volumes.rv,
        rft_module.lung_volumes.rv_tlc,
    ]

    z_score_plot = generate_zscore_plot(plot_params)


    html = render_template('reports/rft-report.html', rft=rft_module, z_score_plot=z_score_plot,image_data=image_data)

    with NamedTemporaryFile(suffix='.pdf') as f:
        options = {
            'page-size': 'A4',
            'enable-local-file-access': None,
            'load-error-handling': 'ignore',
            'disable-smart-shrinking': '',
        }

        pdfkit.from_string(html, f.name, options=options)
        format_param = request.args.get('format', '').lower()
        if format_param == 'base64':
            with open(f.name, 'rb') as pdf_file:
                pdf_content = pdf_file.read()
                pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')

            return jsonify({
                'format': 'base64',
                'content': pdf_base64,
                'mime_type': 'application/pdf'
            })
        else:
            return send_file(f.name, as_attachment=True)