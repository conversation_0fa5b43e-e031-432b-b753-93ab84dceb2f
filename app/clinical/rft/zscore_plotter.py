import math
import re
from typing import Dict, Any, List, Optional, Union
from app.clinical.types import RefType


class ZScorePlotter:
    def __init__(self):
        self.x_axis_min = -5
        self.x_axis_max = 3

        self.ULN = 1.645
        self.LLN = -1.645

        self.normal_color = '#F1F5EB'
        self.abnormal_color = '#FFD1CC'
        self.neutral_color = '#F9F9F9'

    def scale(self, value: float) -> float:
        return ((value + 5) / 8) * 100

    def get_axis_ticks(self) -> List[Dict[str, Any]]:
        return [
            {
                'value': i,
                'position': self.scale(i),
                'is_zero': i == 0,
                'is_darker': i == 0
            }
            for i in range(self.x_axis_min, self.x_axis_max + 1)
        ]

    def get_regions(self, ref_type: RefType) -> List[Dict[str, Any]]:
        if ref_type == RefType.ULN:
            return [
                {'start_pos': 0, 'width': self.scale(self.ULN), 'color': self.normal_color},
                {'start_pos': self.scale(self.ULN), 'width': 100 - self.scale(self.ULN), 'color': self.abnormal_color}
            ]
        elif ref_type == RefType.LLN:
            return [
                {'start_pos': 0, 'width': self.scale(self.LLN), 'color': self.abnormal_color},
                {'start_pos': self.scale(self.LLN), 'width': 100 - self.scale(self.LLN), 'color': self.normal_color}
            ]
        elif ref_type == RefType.RANGE:
            return [
                {'start_pos': 0, 'width': self.scale(self.LLN), 'color': self.abnormal_color},
                {'start_pos': self.scale(self.LLN), 'width': self.scale(self.ULN) - self.scale(self.LLN), 'color': self.normal_color},
                {'start_pos': self.scale(self.ULN), 'width': 100 - self.scale(self.ULN), 'color': self.abnormal_color}
            ]
        else:
            return [{'start_pos': 0, 'width': 100, 'color': self.neutral_color}]

    def get_zscore(self, param) -> Optional[float]:
        if not param:
            return None

        if hasattr(param, 'pred_result') and param.pred_result and param.value is not None:
            try:
                return param.pred_result.get_zscore(param.value)
            except:
                pass

        zscore_str = getattr(param, 'zscore', None)
        if zscore_str and zscore_str != '-':
            if isinstance(zscore_str, (int, float)):
                return float(zscore_str) if not math.isnan(zscore_str) else None
            if isinstance(zscore_str, str):
                match = re.search(r'-?\d+\.?\d*', zscore_str.replace(',', '.'))
                if match:
                    try:
                        return float(match.group())
                    except ValueError:
                        pass
        return None

    def create_dot(self, param, color_class: str) -> Optional[Dict[str, Any]]:
        zscore = self.get_zscore(param)
        if zscore is not None and self.x_axis_min <= zscore <= self.x_axis_max:
            return {
                'position': self.scale(zscore),
                'value': zscore,
                'color_class': color_class
            }
        return None

    def generate_plot(self, plot_params: List[Union[Dict[str, Any], Any]]) -> Dict[str, Any]:
        parameters = []

        for param_data in plot_params:
            if param_data is None:
                continue

            dots = []
            description = ""
            regions = self.get_regions(RefType.NO_REF)

            if isinstance(param_data, dict) and 'sp1' in param_data:
                sp1_param = param_data.get('sp1')
                sp2_param = param_data.get('sp2')

                if sp1_param:
                    description = sp1_param.parameter.description
                    regions = self.get_regions(sp1_param.ref_type)

                    sp1_dot = self.create_dot(sp1_param, 'bg-chart-1')
                    if sp1_dot:
                        dots.append(sp1_dot)

                if sp2_param:
                    sp2_dot = self.create_dot(sp2_param, 'bg-chart-2')
                    if sp2_dot:
                        dots.append(sp2_dot)

            else:
                param = param_data
                if param:
                    description = param.parameter.description
                    regions = self.get_regions(param.ref_type)

                    dot = self.create_dot(param, 'bg-chart-1')
                    if dot:
                        dots.append(dot)

            parameters.append({
                'description': description,
                'regions': regions,
                'dots': dots
            })

        return {
            'axis_ticks': self.get_axis_ticks(),
            'parameters': parameters,
            'sp1_condition': 'Baseline',
            'sp2_condition': 'Post BD'
        }


def generate_zscore_plot(plot_params: List[Union[Dict[str, Any], Any]]) -> Dict[str, Any]:
    """
    Generate z-score plot from array of parameters
    - Auto-detects single vs multi-plot based on structure
    - Configurable ULN/LLN values (default: ±1.645)
    - Configurable colors for normal/abnormal regions
    """
    plotter = ZScorePlotter()
    return plotter.generate_plot(plot_params)