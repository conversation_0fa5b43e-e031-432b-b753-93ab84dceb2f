import base64
from typing import TYPE_CHECKING

from faker import Faker
from sqlalchemy import Column, Integer, ForeignKey, Time, String, Text, TIMESTAMP, LargeBinary
from sqlalchemy.orm import Mapped

from app import db

if TYPE_CHECKING:
    from app.pdf_convert.models import PasPt
    from app.clinical.models import RSession

fake = Faker()


class RftRoutine(db.Model):
    __tablename__ = 'rft_routine'

    rftid = Column(Integer, primary_key=True)
    sessionid = Column(Integer, ForeignKey('r_sessions.sessionid'))
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'))

    testtime = Column(Time)
    testtype = Column(String)
    lab = Column(String)

    report_text = Column(Text)
    report_reportedby = Column(String)
    report_reporteddate = Column(TIMESTAMP)
    report_verifiedby = Column(String)
    report_verifieddate = Column(TIMESTAMP)
    report_authorisedby = Column(String)
    report_authoriseddate = Column(TIMESTAMP)
    report_amendedby = Column(String)
    report_amendeddate = Column(TIMESTAMP)
    report_amendednotes = Column(Text)
    report_status = Column(String)

    # Baseline
    r_bl_fev1 = Column(String)
    r_bl_fvc = Column(String)
    r_bl_vc = Column(String)
    r_bl_fer = Column(String)
    r_bl_fef2575 = Column(String)
    r_bl_pef = Column(String)
    r_pre_condition = Column(String)
    r_bl_tlco = Column(String)
    r_bl_kco = Column(String)
    r_bl_va = Column(String)
    r_bl_hb = Column(String)
    r_bl_ivc = Column(String)
    r_bl_tlc = Column(String)
    r_bl_frc = Column(String)
    r_bl_rv = Column(String)
    r_bl_erv = Column(String)
    r_bl_ic = Column(String)
    r_bl_rvtlc = Column(String)
    r_bl_lvvc = Column(String)
    r_bl_mip = Column(String)
    r_bl_mep = Column(String)
    r_bl_snip = Column(String)
    r_spo2_1 = Column(String)
    r_spo2_2 = Column(String)
    r_bl_feno = Column(String)

    # Post
    r_post_fev1 = Column(String)
    r_post_fvc = Column(String)
    r_post_vc = Column(String)
    r_post_fer = Column(String)
    r_post_fef2575 = Column(String)
    r_post_pef = Column(String)
    r_post_condition = Column(String)

    # Conditions
    r_condition_tl = Column(String)
    r_condition_lv = Column(String)
    r_condition_mrp = Column(String)
    r_condition_feno = Column(String)

    flowvolloop = Column(LargeBinary)  # Base64 encoded image
    bdstatus = Column(String)
    technicalnotes = Column(Text)
    scientist = Column(String)

    # # ABG1
    r_abg1_fio2 = Column(String)
    r_abg1_ph = Column(String)
    r_abg1_pao2 = Column(String)
    r_abg1_paco2 = Column(String)
    r_abg1_sao2 = Column(String)
    r_abg1_cohb = Column(String)
    r_abg1_be = Column(String)
    r_abg1_hco3 = Column(String)
    r_abg1_aapo2 = Column(String)
    r_abg1_shunt = Column(String)

    # # ABG2
    r_abg2_fio2 = Column(String)
    r_abg2_ph = Column(String)
    r_abg2_pao2 = Column(String)
    r_abg2_paco2 = Column(String)
    r_abg2_sao2 = Column(String)
    r_abg2_cohb = Column(String)
    r_abg2_be = Column(String)
    r_abg2_hco3 = Column(String)
    r_abg2_aapo2 = Column(String)
    r_abg2_shunt = Column(String)
    #
    # # Additional
    lungvolumes_method = Column(String)
    # lastupdated_rft = Column(TIMESTAMP)
    # lastupdatedby_rft = Column(String)
    # sample_type = Column(String)
    r_abg1_sampletype = Column(String)
    r_abg2_sampletype = Column(String)
    # device_info = Column(Text)

    # --- Relationships ---
    session: Mapped['RSession'] = db.relationship("RSession", backref="rft_routine")
    patient: Mapped['PasPt'] = db.relationship("PasPt", backref="rft_routine")

    @property
    def flowvolloop_base64(self):
        """Return base64 encoded version of flowvolloop"""
        if self.flowvolloop:
            return base64.b64encode(self.flowvolloop).decode('utf-8')
        return ''

    @classmethod
    def create_fake(cls, patient_id, session_id):
        rft_test = cls(
            **{
                "testtype": "RFTs (Sp1 Sp2 TL LV FeNO MRP ABG Sh Ox)",
                "testtime": "17:37:00",
                "technicalnotes": "All tests acceptable and repeatable. ",
                "sessionid": session_id,
                "scientist": "John Hutchinson",
                "report_text": "Baseline ventilatory function is within normal limits with no significant "
                               "bronchodilator response on this occasion. Carbon monoxide transfer factor, "
                               "uncorrected for haemoglobin, shows a reduction indicating a gas exchange abnormality "
                               "at the alveolar-capillary level or due to pulmonary vascular dysfunction. Lung "
                               "volumes are within normal limits. Maximal respiratory pressures are within the normal "
                               "range. FeNO result is intermediate and may suggest the presence of active "
                               "eosinophilic inflammation. Arterial blood gases reveal normoxaemia with a normal ("
                               "A-a)PO2 gradient and a normal pH.",
                "report_status": "For discussion",
                "report_reporteddate": "2025-06-13T14:00:00",
                "report_reportedby": "dr. bailey, bill",
                "r_spo2_2": "99",
                "r_spo2_1": "96",
                "r_pre_condition": "Baseline",
                "r_post_vc": "5.54",
                "r_post_pef": "8.1",
                "r_post_fvc": "5.18",
                "r_post_fev1": "4.22",
                "r_post_fef2575": "3.7",
                "r_post_condition": "Post BD (Salb MDI)",
                "r_condition_tl": "Baseline",
                "r_condition_lv": "Baseline",
                "r_bl_vc": "5.19",
                "r_bl_va": "6.11",
                "r_bl_tlco": "18.1",
                "r_bl_tlc": "6.55",
                "r_bl_snip": "105",
                "r_bl_rvtlc": "24.274809160305345",
                "r_bl_rv": "1.59",
                "r_bl_pef": "7.2",
                "r_bl_mip": "69",
                "r_bl_mep": "110",
                "r_bl_lvvc": "4.99",
                "r_bl_ivc": "5",
                "r_bl_ic": "2.24",
                "r_bl_hb": "95",
                "r_bl_fvc": "5.22",
                "r_bl_frc": "3.75",
                "r_bl_fev1": "3.91",
                "r_bl_feno": "33",
                "r_bl_fef2575": "3.45",
                "r_bl_erv": "1.15",
                "r_abg2_shunt": "12.141280353200884",
                "r_abg2_sao2": "100",
                "r_abg2_sampletype": "Arterial",
                "r_abg2_ph": "7.39",
                "r_abg2_pao2": "455",
                "r_abg2_paco2": "38",
                "r_abg2_hco3": "20.2",
                "r_abg2_fio2": "100%",
                "r_abg2_be": "1.1",
                "r_abg1_sao2": "97",
                "r_abg1_ph": "7.41",
                "r_abg1_pao2": "88",
                "r_abg1_paco2": "39",
                "r_abg1_hco3": "22.3",
                "r_abg1_cohb": "1.2",
                "r_abg1_be": "1.6",
                "r_abg1_aapo2": "12.6",
                "patientid": patient_id,
                "lungvolumes_method": "Plethysmography",
                "lab": "Main lab",
                "bdstatus": "Nil today",
            }
        )

        return rft_test
