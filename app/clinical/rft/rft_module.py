from functools import cached_property
from typing import Iterable, TYPE_CHECKING

from app.clinical.models import PredRefParameters
from app.clinical.types import ClinicalModule, TestParameter, DbFieldValue, RefType
from app.normal_values.utils import RftGender
from app.normal_values.models import PredSourcexParameter, PredRefSources
from .models import RftRoutine
from ..base_test import BaseTest
from ... import db

if TYPE_CHECKING:
    from app.clinical.models import RSession
    from app.pdf_convert.models import PasPt


class SpirometryTest1(BaseTest):
    name = 'Spirometry1'
    condition = DbFieldValue(db_field='r_pre_condition')

    fev1 = TestParameter(param_name='FEV1', db_field='r_bl_fev1', ref_type=RefType.LLN)
    fvc = TestParameter(param_name='FVC', db_field='r_bl_fvc', ref_type=RefType.LLN)
    vc = TestParameter(param_name='VC', db_field='r_bl_vc', ref_type=RefType.LLN)
    fev1_fvc = TestParameter(param_name='FER', db_field='r_bl_fer', ref_type=RefType.LLN)
    fef2575 = TestParameter(param_name='FEF2575', db_field='r_bl_fef2575', ref_type=RefType.LLN)
    pef = TestParameter(param_name='PEF', db_field='r_bl_pef')
    fer = TestParameter(param_name='FER', db_field='r_bl_fer', ref_type=RefType.LLN)

    @cached_property
    def fev1_vc_param(self) -> PredRefParameters:
        """Lazy load the parameter from database."""
        param = db.session.query(PredRefParameters).filter_by(description='FEV1/VC').first()
        if not param:
            raise ValueError(f"Parameter with name FEV1/VC not found.")

        return param

    @property
    def fev1_vc(self):
        if not self.fev1.value or not self.vc.value:
            return '-'
        return self.fev1_vc_param.format_value(self.fev1.value / self.vc.value)


class SpirometryTest2(BaseTest):
    name = 'Spirometry2'
    condition = DbFieldValue(db_field='r_post_condition')

    fev1 = TestParameter(param_name='FEV1', db_field='r_post_fev1', ref_type=RefType.LLN)
    fvc = TestParameter(param_name='FVC', db_field='r_post_fvc', ref_type=RefType.LLN)
    vc = TestParameter(param_name='VC', db_field='r_post_vc', ref_type=RefType.LLN)
    fev1_fvc = TestParameter(param_name='FER', db_field='r_post_fer', ref_type=RefType.LLN)
    fef2575 = TestParameter(param_name='FEF2575', db_field='r_post_fef2575', ref_type=RefType.LLN)
    pef = TestParameter(param_name='PEF', db_field='r_post_pef')
    fer = TestParameter(param_name='FER', db_field='r_post_fer', ref_type=RefType.LLN)

    @cached_property
    def fev1_vc_param(self) -> PredRefParameters:
        """Lazy load the parameter from database."""
        param = db.session.query(PredRefParameters).filter_by(description='FEV1/VC').first()
        if not param:
            raise ValueError(f"Parameter with name FEV1/VC not found.")

        return param

    @property
    def fev1_vc(self):
        if not self.fev1.value or not self.vc.value:
            return '-'
        return self.fev1_vc_param.format_value(self.fev1.value / self.vc.value)

    def _calculate_change(self, sp2_param, sp1_param):
        """Calculate change (sp2 - sp1) for a parameter."""
        if sp2_param.value is not None and sp1_param.value is not None:
            change = sp2_param.value - sp1_param.value
            return f"{change:+.2f}" if change != 0 else "0.00"
        return '-'

    def _calculate_percent_change(self, sp2_param, sp1_param):
        """Calculate percentage change: ((sp2 - sp1) / sp1_mpv) * 100."""
        if (sp2_param.value is not None and sp1_param.value is not None and 
            sp1_param.pred_result is not None and sp1_param.pred_result.mpv is not None and 
            sp1_param.pred_result.mpv != 0):
            change = sp2_param.value - sp1_param.value
            percent_change = (change / sp1_param.pred_result.mpv) * 100
            return f"{round(percent_change)}%"
        return '-'

    @property
    def fev1_change(self):
        """Change in FEV1 from SP1 to SP2."""
        return self._calculate_change(self.fev1, self.clinical_module.sp1.fev1)

    @property
    def fev1_percent_change(self):
        """Percentage change in FEV1 from SP1 to SP2."""
        return self._calculate_percent_change(self.fev1, self.clinical_module.sp1.fev1)

    @property
    def fvc_change(self):
        """Change in FVC from SP1 to SP2."""
        return self._calculate_change(self.fvc, self.clinical_module.sp1.fvc)

    @property
    def fvc_percent_change(self):
        """Percentage change in FVC from SP1 to SP2."""
        return self._calculate_percent_change(self.fvc, self.clinical_module.sp1.fvc)

    @property
    def vc_change(self):
        """Change in VC from SP1 to SP2."""
        return self._calculate_change(self.vc, self.clinical_module.sp1.vc)

    @property
    def vc_percent_change(self):
        """Percentage change in VC from SP1 to SP2."""
        return self._calculate_percent_change(self.vc, self.clinical_module.sp1.vc)

    @property
    def fev1_fvc_change(self):
        """Change in FEV1/FVC from SP1 to SP2."""
        return self._calculate_change(self.fev1_fvc, self.clinical_module.sp1.fev1_fvc)

    @property
    def fev1_fvc_percent_change(self):
        """Percentage change in FEV1/FVC from SP1 to SP2."""
        return self._calculate_percent_change(self.fev1_fvc, self.clinical_module.sp1.fev1_fvc)

    @property
    def fef2575_change(self):
        """Change in FEF25-75 from SP1 to SP2."""
        return self._calculate_change(self.fef2575, self.clinical_module.sp1.fef2575)

    @property
    def fef2575_percent_change(self):
        """Percentage change in FEF25-75 from SP1 to SP2."""
        return self._calculate_percent_change(self.fef2575, self.clinical_module.sp1.fef2575)

    @property
    def pef_change(self):
        """Change in PEF from SP1 to SP2."""
        return self._calculate_change(self.pef, self.clinical_module.sp1.pef)

    @property
    def pef_percent_change(self):
        """Percentage change in PEF from SP1 to SP2."""
        return self._calculate_percent_change(self.pef, self.clinical_module.sp1.pef)


class CoTransferFactor(BaseTest):
    name = 'CoTransferFactor'
    clinical_module: 'RftModule'
    condition = DbFieldValue(db_field='r_condition_tl')

    tlco = TestParameter(param_name='TLCO', db_field='r_bl_tlco', ref_type=RefType.RANGE)
    va = TestParameter(param_name='VA', db_field='r_bl_va', ref_type=RefType.LLN)
    kco = TestParameter(param_name='KCO', db_field='r_bl_kco', ref_type=RefType.RANGE)
    # todo: verify if IVC and VI are same
    vi = TestParameter(param_name='IVC', db_field='r_bl_ivc')
    hb = TestParameter(param_name='Hb', db_field='r_bl_hb')

    @property
    def tlco_hb(self):
        if not self.tlco.parameter or not self.tlco.value or not self.hb_factor:
            return '-'
        
        try:
            if not isinstance(self.hb_factor, (int, float)) or not isinstance(self.tlco.value, (int, float)):
                return '-'
            result = self.hb_factor * self.tlco.value
            return self.tlco.parameter.format_value(result)
        except (TypeError, ValueError):
            return '-'

    @property
    def kco_hb(self):
        if not self.kco.parameter or not self.kco.value or not self.hb_factor:
            return '-'

        try:
            if not isinstance(self.hb_factor, (int, float)) or not isinstance(self.kco.value, (int, float)):
                return '-'
            result = self.hb_factor * self.kco.value
            return self.kco.parameter.format_value(result)
        except (TypeError, ValueError):
            return '-'

    @property
    def hb_factor(self):
        if not self.hb.parameter:
            return None

        try:
            hb_factor_value = (self.hb.value or 0) / (self.hb.parameter.units_convert_trad_to_si or 1)
            hb_eq_func = self.clinical_module.hb_eq
            result = hb_eq_func(hb_factor_value)
            # Ensure we return a numeric value or None
            if isinstance(result, (int, float)):
                return result
            return None
        except (TypeError, ValueError, ZeroDivisionError):
            return None


class LungVolumes(BaseTest):
    name = 'LungVolumes'
    condition = DbFieldValue(db_field='r_condition_lv')
    method = DbFieldValue(db_field='lungvolumes_method')

    tlc = TestParameter(param_name='TLC', db_field='r_bl_tlc', ref_type=RefType.RANGE)
    lvvc = TestParameter(param_name='LvVC', db_field='r_bl_lvvc', ref_type=RefType.LLN)
    ic = TestParameter(param_name='IC', db_field='r_bl_ic', ref_type=RefType.LLN)
    frc = TestParameter(param_name='FRC', db_field='r_bl_frc', ref_type=RefType.RANGE)
    erv = TestParameter(param_name='ERV', db_field='r_bl_erv', ref_type=RefType.LLN)
    rv = TestParameter(param_name='RV', db_field='r_bl_rv', ref_type=RefType.LLN)
    rv_tlc = TestParameter(param_name='RV/TLC', db_field='r_bl_rvtlc', ref_type=RefType.ULN)


class NitricOxide(BaseTest):
    name = 'ExhaledNitricOxide'
    condition = DbFieldValue(db_field='r_condition_feno')

    feno = TestParameter(param_name='FeNO', db_field='r_bl_feno', ref_type=RefType.ULN)


class MRPs(BaseTest):
    name = 'MRPs'
    condition = DbFieldValue(db_field='r_condition_mrp')

    mip = TestParameter(param_name='MIP', db_field='r_bl_mip', ref_type=RefType.LLN)
    mep = TestParameter(param_name='MEP', db_field='r_bl_mep', ref_type=RefType.LLN)
    snip = TestParameter(param_name='SNIP', db_field='r_bl_snip', ref_type=RefType.LLN)


class BloodGasses(BaseTest):
    name = 'BloodGasses'
    sample_type_1 = DbFieldValue(db_field='r_abg1_sampletype')
    sample_type_2 = DbFieldValue(db_field='r_abg2_sampletype')

    fio1 = DbFieldValue(db_field='r_abg1_fio2')
    fio2 = DbFieldValue(db_field='r_abg2_fio2')

    ph1 = TestParameter(param_name='pH', db_field='r_abg1_ph', ref_type=RefType.RANGE)
    ph2 = TestParameter(param_name='pH', db_field='r_abg2_ph', ref_type=RefType.RANGE)

    paco21 = TestParameter(param_name='PaCO2', db_field='r_abg1_paco2', ref_type=RefType.RANGE)
    paco22 = TestParameter(param_name='PaCO2', db_field='r_abg2_paco2', ref_type=RefType.RANGE)

    pao21 = TestParameter(param_name='PaO2', db_field='r_abg1_pao2', ref_type=RefType.LLN)
    pao22 = TestParameter(param_name='PaO2', db_field='r_abg2_pao2', ref_type=RefType.LLN)

    hco31 = TestParameter(param_name='HCO3', db_field='r_abg1_hco3', ref_type=RefType.RANGE)
    hco32 = TestParameter(param_name='HCO3', db_field='r_abg2_hco3', ref_type=RefType.RANGE)

    be1 = TestParameter(param_name='BE', db_field='r_abg1_be', ref_type=RefType.RANGE)
    be2 = TestParameter(param_name='BE', db_field='r_abg2_be', ref_type=RefType.RANGE)

    sao21 = TestParameter(param_name='SaO2', db_field='r_abg1_sao2', ref_type=RefType.RANGE)
    sao22 = TestParameter(param_name='SaO2', db_field='r_abg2_sao2', ref_type=RefType.RANGE)

    spo21 = TestParameter(param_name='SaO2', db_field='r_spo2_1', ref_type=RefType.RANGE)
    spo22 = TestParameter(param_name='SaO2', db_field='r_spo2_2', ref_type=RefType.RANGE)

    cohb1 = TestParameter(param_name='COHb', db_field='r_abg1_cohb')
    cohb2 = TestParameter(param_name='COHb', db_field='r_abg2_cohb')

    aapo21 = TestParameter(param_name='A-aPO2', db_field='r_abg1_aapo2', ref_type=RefType.ULN)
    aapo22 = TestParameter(param_name='A-aPO2', db_field='r_abg2_aapo2', ref_type=RefType.ULN)

    shunt1 = TestParameter(param_name='Shunt', db_field='r_abg1_shunt', ref_type=RefType.ULN)
    shunt2 = TestParameter(param_name='Shunt', db_field='r_abg2_shunt', ref_type=RefType.ULN)


class RftModule(ClinicalModule):
    """Extended RftRoutine with module functionality."""
    _rft_routine: RftRoutine = None

    _config_tests = [
        SpirometryTest1(), SpirometryTest2(), CoTransferFactor(), LungVolumes(),
        NitricOxide(), MRPs(), BloodGasses()
    ]

    def __init__(self, rft_routine: RftRoutine):
        self._rft_routine = rft_routine

        self.sp1 = SpirometryTest1(self)
        self.sp2 = SpirometryTest2(self)
        self.co_transfer = CoTransferFactor(self)
        self.lung_volumes = LungVolumes(self)
        self.feno = NitricOxide(self)
        self.mrps = MRPs(self)
        self.blood_gasses = BloodGasses(self)

    def __getattr__(self, item):
        try:
            return super().__getattribute__(item)
        except AttributeError:
            pass

        if hasattr(self._rft_routine, item):
            return getattr(self._rft_routine, item)
        return super().__getattribute__(item)

    def get_db_value(self, item):
        if hasattr(self._rft_routine, item):
            return getattr(self._rft_routine, item)
        return super().__getattribute__(item)

    def get_test_session(self) -> 'RSession':
        return self._rft_routine.session

    def get_patient(self) -> 'PasPt':
        return self._rft_routine.patient

    def get_parameter_ids(self) -> Iterable[int]:
        return {
            *(pv.parameter.id for pv in self.sp1.get_parameter_values()),
            *(pv.parameter.id for pv in self.co_transfer.get_parameter_values()),
            *(pv.parameter.id for pv in self.lung_volumes.get_parameter_values()),
            *(pv.parameter.id for pv in self.feno.get_parameter_values()),
            *(pv.parameter.id for pv in self.mrps.get_parameter_values()),
            *(pv.parameter.id for pv in self.blood_gasses.get_parameter_values()),
        }

    def get_sources_by_test(self):
        """Get normal value sources organized by test components."""
        config = {
            'Spirometry': [self.sp1, self.sp2],
            'TLCO': [self.co_transfer],
            'Lung Volumes': [self.lung_volumes],
            # 'MRPs': [self.mrps],
            'FeNO': [self.feno],
            'Blood Gas': [self.blood_gasses]
        }

        sources_by_test = {}

        for test_name, tests in config.items():
            all_param_ids = set()

            for test in tests:
                for field_name, field_value in test._fields.items():
                    if isinstance(field_value, TestParameter) and field_value.parameter:
                        all_param_ids.add(field_value.parameter.id)

            if not all_param_ids:
                continue

            sourcex_records = PredSourcexParameter.query.filter(
                PredSourcexParameter.paramid.in_(list(all_param_ids))
            ).all()

            if sourcex_records:
                source_ids = [record.sourceid for record in sourcex_records]
                sources = PredRefSources.query.filter(
                    PredRefSources.id.in_(source_ids)
                ).all()

                unique_sources = list({source.source for source in sources if source.source})
                if unique_sources:
                    sources_by_test[test_name] = unique_sources

        return sources_by_test

    @property
    def hb_eq(self):
        age = self.metadata.age
        gender = self.metadata.gender

        if gender == RftGender.male:
            if 16 <= age <= 120:
                return lambda hb: (10.22 + hb) / (1.7 * hb) if hb != 0 else None
            if 6 <= age < 16:
                return lambda hb: (9.38 + hb) / (1.7 * hb) if hb != 0 else None
        if gender == RftGender.female:
            if 16 <= age <= 120:
                return lambda hb: (9.38 + hb) / (1.7 * hb) if hb != 0 else None

        return lambda x: None