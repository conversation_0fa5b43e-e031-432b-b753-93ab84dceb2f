<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Respiratory Function Test Report</title>
    <meta name="pdfkit-page-size" content="A4" />
    <meta name="pdfkit-margin-top" content="0mm" />
    <meta name="pdfkit-margin-bottom" content="0mm" />
    <meta name="pdfkit-margin-left" content="0mm" />
    <meta name="pdfkit-margin-right" content="0mm" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      @media print {
        body {
          zoom: 172%;
        }
      }

      body {
        font-family: Figtree, sans-serif;
        color: #333;
        background-color: white;
        padding: 16px;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        padding-bottom: 4px;
      }

      .header img {
        height: 20px;
        width: auto;
      }

      .contact-info {
        display: flex;
        align-items: center;
      }

      .contact-info .contact-text {
        font-size: 8px;
        margin-left: 3px;
        white-space: nowrap;
        color: #777777;
      }

      .row {
        display: flex;
        align-items: center;
        margin-right: 10px;
      }

      .contact-info svg {
        width: 10px;
        height: 10px;
        flex-shrink: 0;
      }

      .divider {
        width: 100%;
        height: 1px;
        background: #dcdcdc;
        margin-bottom: 8px;
      }

      .test-info {
        width: 100%;
        margin-top: 16px;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-bottom: 8px;
      }

      .test-name {
        font-size: 10px;
        font-weight: 500;
        letter-spacing: 0.05em;
        color: #595959;
        text-transform: uppercase;
        margin-bottom: 5px;
      }

      .patient-name {
        font-size: 18px;
        font-weight: bold;
        letter-spacing: -0.02em;
        color: #000000;
        line-height: 20px;
      }

      .left-section {
        display: flex;
        gap: 10px;
        width: max-content;
      }

      .light-gray {
        color: #777777;
        font-size: 8px !important;
        line-height: 130%;
        margin-right: 4px;
      }

      .text-small {
        font-size: 8px;
        line-height: 130%;
      }

      .medical-card {
        width: 100%;
        padding: 8px 10px;
        background: #F7F7F7;
        overflow: hidden;
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        margin-bottom: 16px;
      }

      .main-info-row {
        align-self: stretch;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 6px;
      }

      .info-column {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
      }

      .info-row {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 5px;
      }

      .info-row:last-child {
        margin-bottom: 0;
      }

      .info-label {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        color: #595959;
        font-size: 8px;
        font-weight: 400;
        line-height: 10.40px;
        word-wrap: break-word;
      }

      .info-value {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        color: #404040;
        font-size: 8px;
        font-weight: 400;
        line-height: 10.40px;
        word-wrap: break-word;
      }

      .address-row {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        gap: 2px;
        margin-bottom: 3px;
      }

      .clinical-notes {
        align-self: stretch;
        display: flex;
        font-size: 8px;
      }

      .notes-label {
        color: #595959;
        font-size: 8px;
        font-weight: 400;
        line-height: 10px;
        word-wrap: break-word;
      }

      .notes-text {
        color: #404040;
        font-size: 8px;
        font-weight: 400;
        line-height: 10px;
        word-wrap: break-word;
      }

      .test-sp {
        margin-top: 16px;
        width: 100%;
        display: flex;
        justify-content: flex-start;
      }

      .labels {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-end;
        width: 95px;
        font-size: 8px;
        font-weight: 500;
        margin-right: 6px;
      }

      .labels-heading {
        font-weight: bold;
        font-size: 8px;
        text-transform: uppercase;
        text-align: right;
        margin-bottom: 4px;
        height: 16px;
        line-height: 8px;
        display: flex;
        align-items: flex-end;
        color: #000000;
      }

      .labels-text {
        line-height: 12px;
        height: 12px;
        font-size: 6.5px;
        letter-spacing: -0.02em;
        text-transform: uppercase;
        text-align: right;
        color: #000000;
        white-space: nowrap;
      }

      .table-header {
        padding: 0 2px;
        border: 1px solid #DCDCDC;
        border-radius: 2px;
        height: 14px;
        width: 190px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }

      .table-header-cell {
        color: #404040;
        font-weight: 600;
        font-size: 7px;
        line-height: 14px;
        letter-spacing: 0.02em;
        width: 100%;
        text-align: right;
        text-transform: uppercase;
        padding: 0 2px;
      }

      .baseline {
        font-style: italic;
        margin-top: 7px;
        margin-bottom: 4px;
        font-size: 8px;
        line-height: 8px;
        color: #000000;
      }

      .table-body {
        border: 1px solid #ededed;
        border-radius: 2px;
        font-size: 7.5px;
        font-weight: 500;
        color: #595959;
        width: 190px;
      }

      .table-row {
        display: flex;
        height: 12px;
        border-bottom: 1px solid #ededed;
        padding: 0 2px;
      }

      .table-row:last-child {
        border-bottom: 0;
      }

      .table-cell {
        width: 100%;
        line-height: 12px;
        text-align: right;
        color: #000000;
        padding: 0 2.75px;
      }

      .green-dot {
        height: 4.5px;
        width: 4.5px;
        background-color: #4A827F;
        border-radius: 50%;
        display: inline-block;
        margin-right: 2px;
      }

      .yellow-dot {
        height: 4.2px;
        width: 4.2px;
        background-color: #E5961B;
        border-radius: 10%;
        display: inline-block;
        margin-right: 2px;
      }

      .center {
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }

      .media {
        margin-top: 16px;
        margin-left: 12px;
        width: 100%;
        border: 1px solid #DCDCDC;
        height: 120px;
        border-radius: 4px;
      }

      .legend-text {
        font-size: 8px;
        font-weight: 500;
        line-height: 10px;
        color: #969696;
      }

      .horizontal {
        display: flex;
        align-items: center;
      }

      .chart {
        padding: 6px;
      }

      .z-score-scale {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 216px;
      }

      .z-score-scale-text {
        font-size: 7px;
        font-weight: 400;
        line-height: 10px;
        color: #595959;
      }

      .z-score-plot {
        margin-top: 4px;
      }

      .z-score-plot-container {
        height: 13px;
        width: 216px;
        z-index: 1;
        background-color: #FFE8E6;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 5px;
        margin-bottom: 2px;
      }

      .z-score-row {
        display: flex;
        justify-items: center;
      }

      .z-score-plot-lable {
        font-size: 7px;
        font-weight: 600;
        line-height: 10px;
        color: #595959;
        letter-spacing: 0.02em;
        text-transform: uppercase;
        margin-left: 5px;
      }

      .vertical-divider {
        height: 12px;
        width: 1px;
        border-left: 1px dashed #0000001F;
      }

      .main-container {
        display: flex;
        flex-wrap: wrap;
        margin-top: 16px;
        width: 100%;
        box-sizing: border-box;
      }

      .grid-item {
        display: flex;
        flex-direction: column;
        width: calc(50% - 12px); 
        min-height: 200px;
        box-sizing: border-box;
        align-items: flex-start;
        margin-right: 12px;
        margin-bottom: 8px;
      }

      .grid-item:nth-child(2n) {
        margin-right: 0;
      }

      @media print {
        .main-container {
          display: flex !important;
          flex-wrap: wrap !important;
          width: 100% !important;
          margin-top: 16px !important;
        }
        
        .grid-item {
          width: calc(50% - 12px) !important;
          float: left !important; 
          display: inline-block !important; 
          vertical-align: top !important;
          break-inside: avoid !important;
          page-break-inside: avoid !important;
          min-height: 200px !important;
          box-sizing: border-box !important;
          margin-right: 12px !important; 
          margin-bottom: 8px !important; 
        }
        
        .grid-item:nth-child(2n) {
          margin-right: 0 !important;
        }
        
        .grid-item:nth-child(2n+1) {
          clear: left !important;
        }
        
        .main-container::after {
          content: "";
          display: table;
          clear: both;
        }
      }

      .challenge-section {
        margin-top: 24px;
        width: 100%;
      }

      .challenge-title {
        font-size: 10px;
        font-weight: bold;
        letter-spacing: 0.05em;
        color: #000000;
        text-transform: uppercase;
        margin-bottom: 16px;
      }

      .challenge-tables {
        display: flex;
        gap: 24px;
        align-items: flex-start;
      }

      .table-header3 {
        padding: 0 2px;
        border: 1px solid #DCDCDC;
        border-radius: 2px;
        height: 14px;
        width: 240px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }

      .table-body3 {
        border: 1px solid #ededed;
        border-radius: 2px;
        font-size: 7.5px;
        font-weight: 500;
        color: #595959;
        width: 240px;
      }

      .response-table {
        display: flex;
        flex-direction: column;
      }

      .response-title {
        font-size: 8px;
        font-weight: bold;
        text-transform: uppercase;
        color: #000000;
        margin-bottom: 4px;
      }

      .dose-response {
        margin-top: 8px;
        font-size: 8px;
        color: #000000;
      }

      .dose-label {
        font-weight: bold;
        text-transform: uppercase;
      }

      .dose-table-header {
        border: 1px solid #DCDCDC;
        border-radius: 2px;
        height: 14px;
        width: 100%;
        display: flex;
        font-size: 7px;
        font-weight: 600;
        color: #404040;
        margin-bottom: 2px;
        padding: 0 2px;
        align-items: center;
      }

      .dose-table-body {
        border: 1px solid #ededed;
        border-radius: 2px;
        font-size: 7.5px;
        color: #000000;
        width: 100%;
      }

      .dose-table-row {
        display: flex;
        height: 12px;
        border-bottom: 1px solid #ededed;
        padding: 0 2px;
        align-items: center;
      }

      .dose-table-row:last-child {
        border-bottom: 0;
      }

      .dose-table-cell {
        line-height: 12px;
        padding: 0 2px;
      }

      .dose-table-cell:first-child {
        width: 60px;
        text-align: left;
      }

      .dose-table-cell:nth-child(2) {
        width: 60px;
        text-align: right;
      }

      .dose-table-cell:last-child {
        width: 70px;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <img
        src="https://cdn.prod.website-files.com/61b33c9874bfaa2230d313aa/61b9760563882558093eb7df_logo.svg"
      />
      <div class="contact-info">
        <div class="row">
          <svg
            width="10"
            height="10"
            viewBox="0 0 10 10"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M4.80488 5.19683C6.467 6.85848 6.84406 4.93613 7.90234 5.99367C8.9226 7.01365 9.509 7.218 8.21634 8.5103C8.05442 8.64043 7.02566 10.2059 3.41024 6.59153C-0.205626 2.97667 1.35898 1.94685 1.48914 1.78498C2.78493 0.489104 2.98577 1.07891 4.00603 2.09889C5.06431 3.15687 3.14276 3.53517 4.80488 5.19683Z"
              fill="#595959"
            />
          </svg>
          <span class="contact-text">WA: 1300 130 930</span>
        </div>

        <div class="row">
          <svg
            width="10"
            height="10"
            viewBox="0 0 10 10"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M4.80488 5.19683C6.467 6.85848 6.84406 4.93613 7.90234 5.99367C8.9226 7.01365 9.509 7.218 8.21634 8.5103C8.05442 8.64043 7.02566 10.2059 3.41024 6.59153C-0.205626 2.97667 1.35898 1.94685 1.48914 1.78498C2.78493 0.489104 2.98577 1.07891 4.00603 2.09889C5.06431 3.15687 3.14276 3.53517 4.80488 5.19683Z"
              fill="#595959"
            />
          </svg>
          <span class="contact-text">VIC: 03 9912 9001</span>
        </div>

        <div class="row">
          <svg
            width="10"
            height="10"
            viewBox="0 0 10 10"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.05808 1.25C7.61683 1.25 8.15433 1.47083 8.54975 1.86708C8.94558 2.2625 9.16683 2.79583 9.16683 3.35417V6.64583C9.16683 7.80833 8.221 8.75 7.05808 8.75H2.94183C1.77891 8.75 0.833496 7.80833 0.833496 6.64583V3.35417C0.833496 2.19167 1.77475 1.25 2.94183 1.25H7.05808ZM7.52933 3.41667C7.44183 3.41208 7.3585 3.44167 7.29558 3.5L5.41683 5C5.17516 5.20042 4.82891 5.20042 4.5835 5L2.7085 3.5C2.57891 3.40417 2.39975 3.41667 2.29183 3.52917C2.17933 3.64167 2.16683 3.82083 2.26225 3.94583L2.31683 4L4.21266 5.47917C4.446 5.6625 4.72891 5.7625 5.02516 5.7625C5.32058 5.7625 5.6085 5.6625 5.84141 5.47917L7.721 3.975L7.75433 3.94167C7.85391 3.82083 7.85391 3.64583 7.74975 3.525C7.69183 3.46292 7.61225 3.425 7.52933 3.41667Z"
              fill="#595959"
            />
          </svg>
          <span class="contact-text">Enquiries: <EMAIL></span>
        </div>
      </div>
    </div>
    <div class="divider"></div>

    <div class="test-info">
      <div>
        <div class="test-name">Respiratory Function Report</div>
        <div class="patient-name">John.</div>
      </div>

      <div class="left-section">
        <div class="text-small">
          <div class="row"><span class="light-gray text-small">Test Date:</span>06/06/2025</div>
          <div class="row" style="margin-top: 4px"><span class="light-gray text-small">Test Time:</span>10:00</div>
        </div>
        <div class="text-small">
          <div class="row"><span class="light-gray text-small">To:</span>Dr James Wardell</div>
          <div class="row" style="margin-top: 4px"><span class="light-gray">CC:</span>Dr G Pee</div>
        </div>
      </div>
    </div>

    <div class="medical-card">
      <div class="main-info-row">
        <div class="info-column">
          <div class="info-row">
            <div class="info-label">MRN:</div>
            <div class="info-value">123456 - Kingsmill Hospital</div>
          </div>
          <div class="info-row">
            <div class="info-label">DOB:</div>
            <div class="info-value">Nov 16, 1999</div>
          </div>
          <div class="info-row">
            <div class="info-label">Gender:</div>
            <div class="info-value">Male</div>
          </div>
        </div>
        <div class="info-column">
          <div class="info-row">
            <div class="info-label">Height (cm):</div>
            <div class="info-value">170.0</div>
          </div>
          <div class="info-row">
            <div class="info-label">Weight (kg):</div>
            <div class="info-value">70.0</div>
          </div>
          <div class="info-row">
            <div class="info-label">BMI:</div>
            <div class="info-value">24.2</div>
          </div>
        </div>
        <div class="info-column">
          <div class="info-row">
            <div class="info-label">Smoking Exposure:</div>
            <div class="info-value">Current Smoker</div>
          </div>
          <div class="info-row">
            <div class="info-label">Pack years:</div>
            <div class="info-value">14.4</div>
          </div>
          <div class="info-row">
            <div class="info-label">Last Smoked:</div>
            <div class="info-value">00:00</div>
          </div>
        </div>
        <div class="info-column">
          <div class="info-row">
            <div class="info-label">Last BD:</div>
            <div class="info-value">Nil today</div>
          </div>
          <div class="address-row">
            <div class="info-label">Address:</div>
            <div class="info-value">1 Street St, Nowhere, 3000</div>
          </div>
        </div>
      </div>
      <div class="clinical-notes">
        <span class="notes-label">Clinical Notes: </span>
        <span class="notes-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</span>
      </div>
    </div>

    <div style="margin-top: 24px; margin-bottom: 8px;">
      <div style="font-size: 10px; font-weight: bold; letter-spacing: 0.05em; color: #000000; text-transform: uppercase;">
        Bronchial Challenge Test: Mannitol (Standard protocol)
      </div>
    </div>

    <div class="main-container">
      <div class="grid-item">
        <div class="test-sp">
          <div class="labels">
            <div class="labels-heading">Spirometry</div>
            <div class="labels-text">fev1 (L)</div>
            <div class="labels-text">fvc (L)</div>
            <div class="labels-text">vc (L)</div>
            <div class="labels-text">fev1/fvc (%)</div>
            <div class="labels-text">fev1/vc (%)</div>
            <div class="labels-text">fef25-75 (L/sec)</div>
            <div class="labels-text">pef (L/sec)</div>
          </div>

          <div>
            <div class="table-header">
              <div class="table-header-cell">Ref</div>
              <div class="table-header-cell">Result</div>
              <div class="table-header-cell">
                <div class="center"><span class="green-dot"></span>Z-Score</div>
              </div>
              <div class="table-header-cell">%pred</div>
            </div>

            <div class="baseline">Baseline</div>

            <div class="table-body">
              <div class="table-row">
                <div class="table-cell">1.4</div>
                <div class="table-cell">4.2</div>
                <div class="table-cell">3.2</div>
                <div class="table-cell">2.3</div>
              </div>
              <div class="table-row">
                <div class="table-cell">6.54</div>
                <div class="table-cell">4.44</div>
                <div class="table-cell">3.2</div>
                <div class="table-cell">2.3</div>
              </div>
              <div class="table-row">
                <div class="table-cell">3.3</div>
                <div class="table-cell">4.44</div>
                <div class="table-cell">20.3</div>
                <div class="table-cell">5.5</div>
              </div>
              <div class="table-row">
                <div class="table-cell">2.33</div>
                <div class="table-cell">3.33</div>
                <div class="table-cell">4.44</div>
                <div class="table-cell">4.44</div>
              </div>
              <div class="table-row">
                <div class="table-cell">3.33</div>
                <div class="table-cell">2.23</div>
                <div class="table-cell">3.33</div>
                <div class="table-cell">5.435</div>
              </div>
              <div class="table-row">
                <div class="table-cell">4.42</div>
                <div class="table-cell">0.543</div>
                <div class="table-cell">9.2</div>
                <div class="table-cell">3.21</div>
              </div>
              <div class="table-row">
                <div class="table-cell">4.81</div>
                <div class="table-cell">9.21</div>
                <div class="table-cell">2.123</div>
                <div class="table-cell">10.23</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="grid-item">
        <div class="media" style="margin: 0; width: 100%; height: 100%;">
          <div class="chart">
            <div class="horizontal">
              <div class="horizontal legend-text" style="margin-right: 6px"><span class="green-dot"></span>Baseline</div>
              <div class="horizontal legend-text"><span class="yellow-dot"></span>Post BD (Salb MDI)</div>
            </div>

            <div class="z-score-plot">
              <div class="z-score-scale">
                <div class="z-score-scale-text">-5</div>
                <div class="z-score-scale-text">-4</div>
                <div class="z-score-scale-text">-3</div>
                <div class="z-score-scale-text">-2</div>
                <div class="z-score-scale-text">-1</div>
                <div class="z-score-scale-text">0</div>
                <div class="z-score-scale-text">1</div>
                <div class="z-score-scale-text">2</div>
                <div class="z-score-scale-text">3</div>
              </div>
              

              <div class="z-score-row">
                <div class="z-score-plot-container">
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider " style="margin-left:-1px "></div>
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider"></div>
                </div>

                <div class="z-score-plot-lable">
                  fev1
                </div>
              </div>
              <div class="z-score-row">
                <div class="z-score-plot-container">
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider " style="margin-left:-1px "></div>
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider"></div>
                </div>

                <div class="z-score-plot-lable">
                  fvc
                </div>
              </div>
              <div class="z-score-row">
                <div class="z-score-plot-container">
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider " style="margin-left:-1px "></div>
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider"></div>
                </div>

                <div class="z-score-plot-lable">
                  vc
                </div>
              </div>
              <div class="z-score-row">
                <div class="z-score-plot-container">
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider " style="margin-left:-1px "></div>
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider"></div>
                </div>

                <div class="z-score-plot-lable">
                  fev1/fvc
                </div>
              </div>
              <div class="z-score-row">
                <div class="z-score-plot-container">
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider " style="margin-left:-1px "></div>
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider"></div>
                </div>

                <div class="z-score-plot-lable">
                  fev1/vc
                </div>
              </div>
              <div class="z-score-row">
                <div class="z-score-plot-container">
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider " style="margin-left:-1px "></div>
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider"></div>
                </div>

                <div class="z-score-plot-lable">
                  fef25-75
                </div>
              </div>
              <div class="z-score-row">
                <div class="z-score-plot-container">
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 2px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider" style="margin-left: 4px"></div>
                  <div class="vertical-divider " style="margin-left:-1px "></div>
                  <div class="vertical-divider"></div>
                  <div class="vertical-divider"></div>
                </div>

                <div class="z-score-plot-lable">
                  pef
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="grid-item">
        <div class="simple-response-table">
          <div class="response-title">Response</div>
          
          <div class="dose-table-header">
            <div class="dose-table-cell">Level</div>
            <div class="dose-table-cell">FEV1 (L)</div>
            <div class="dose-table-cell">%Change</div>
          </div>
          
          <div class="dose-table-body">
            <div class="dose-table-row">
              <div class="dose-table-cell">B/L</div>
              <div class="dose-table-cell">3.33</div>
              <div class="dose-table-cell">+4%</div>
            </div>
            <div class="dose-table-row">
              <div class="dose-table-cell">Control</div>
              <div class="dose-table-cell">3.21</div>
              <div class="dose-table-cell">+0%</div>
            </div>
            <div class="dose-table-row">
              <div class="dose-table-cell">5mg</div>
              <div class="dose-table-cell">3.11</div>
              <div class="dose-table-cell">-3%</div>
            </div>
            <div class="dose-table-row">
              <div class="dose-table-cell">15mg</div>
              <div class="dose-table-cell">2.98</div>
              <div class="dose-table-cell">-7%</div>
            </div>
            <div class="dose-table-row">
              <div class="dose-table-cell">35mg</div>
              <div class="dose-table-cell">2.81</div>
              <div class="dose-table-cell">-13%</div>
            </div>
            <div class="dose-table-row">
              <div class="dose-table-cell">75mg</div>
              <div class="dose-table-cell">2.74</div>
              <div class="dose-table-cell">-15%</div>
            </div>
            <div class="dose-table-row">
              <div class="dose-table-cell">155mg</div>
              <div class="dose-table-cell">2.61</div>
              <div class="dose-table-cell">-19%</div>
            </div>
            <div class="dose-table-row">
              <div class="dose-table-cell">315mg</div>
              <div class="dose-table-cell">2.41</div>
              <div class="dose-table-cell">-25%</div>
            </div>
            <div class="dose-table-row">
              <div class="dose-table-cell">475mg</div>
              <div class="dose-table-cell">2.41</div>
              <div class="dose-table-cell">-25%</div>
            </div>
            <div class="dose-table-row">
              <div class="dose-table-cell">635mg</div>
              <div class="dose-table-cell">2.22</div>
              <div class="dose-table-cell">-31%</div>
            </div>
            <div class="dose-table-row">
              <div class="dose-table-cell">Post BD</div>
              <div class="dose-table-cell">3.59</div>
              <div class="dose-table-cell">+12%</div>
            </div>
          </div>

          <div class="dose-response">
            <span class="dose-label">Dose Response:</span><br>
            PD15 = 61 mg Mannitol
          </div>
        </div>
      </div>

      <div class="grid-item">
        <div class="dose-response-chart" style="border: 1px solid #DCDCDC; border-radius: 4px; padding: 10px; width: 100%; height: 100%;">
          <svg width="100%" height="180" viewBox="0 0 280 180">
            <text x="140" y="15" text-anchor="middle" font-size="10" font-weight="bold" fill="#000">FEV1 (% of control)</text>
            
            <g stroke="#ddd" stroke-width="0.5">
              <line x1="40" y1="30" x2="260" y2="30"/>
              <line x1="40" y1="45" x2="260" y2="45"/>
              <line x1="40" y1="60" x2="260" y2="60"/>
              <line x1="40" y1="75" x2="260" y2="75"/>
              <line x1="40" y1="90" x2="260" y2="90"/>
              <line x1="40" y1="105" x2="260" y2="105"/>
              <line x1="40" y1="120" x2="260" y2="120"/>
              <line x1="40" y1="135" x2="260" y2="135"/>
            </g>
            
            <g stroke="#ddd" stroke-width="0.5">
              <line x1="40" y1="30" x2="40" y2="135"/>
              <line x1="65" y1="30" x2="65" y2="135"/>
              <line x1="85" y1="30" x2="85" y2="135"/>
              <line x1="105" y1="30" x2="105" y2="135"/>
              <line x1="125" y1="30" x2="125" y2="135"/>
              <line x1="145" y1="30" x2="145" y2="135"/>
              <line x1="165" y1="30" x2="165" y2="135"/>
              <line x1="185" y1="30" x2="185" y2="135"/>
              <line x1="205" y1="30" x2="205" y2="135"/>
              <line x1="225" y1="30" x2="225" y2="135"/>
              <line x1="260" y1="30" x2="260" y2="135"/>
            </g>
            
            <g font-size="8" fill="#666">
              <text x="35" y="35" text-anchor="end">120</text>
              <text x="35" y="50" text-anchor="end">110</text>
              <text x="35" y="65" text-anchor="end">100</text>
              <text x="35" y="80" text-anchor="end">90</text>
              <text x="35" y="95" text-anchor="end">80</text>
              <text x="35" y="110" text-anchor="end">70</text>
              <text x="35" y="125" text-anchor="end">60</text>
              <text x="35" y="140" text-anchor="end">50</text>
            </g>
            
            <g font-size="7" fill="#666">
              <text x="40" y="150" text-anchor="middle">B/L</text>
              <text x="65" y="150" text-anchor="middle">Control</text>
              <text x="85" y="150" text-anchor="middle">5</text>
              <text x="105" y="150" text-anchor="middle">15</text>
              <text x="125" y="150" text-anchor="middle">35</text>
              <text x="145" y="150" text-anchor="middle">75</text>
              <text x="165" y="150" text-anchor="middle">155</text>
              <text x="185" y="150" text-anchor="middle">315</text>
              <text x="205" y="150" text-anchor="middle">475</text>
              <text x="225" y="150" text-anchor="middle">635</text>
              <text x="260" y="150" text-anchor="middle">Post BD</text>
            </g>
            
            <text x="150" y="170" text-anchor="middle" font-size="9" fill="#666">Log cumulative dose (mg)</text>
            
            <line x1="40" y1="95" x2="260" y2="95" stroke="#4A90E2" stroke-width="1" stroke-dasharray="3,3"/>
            

            <line x1="40" y1="59" x2="65" y2="65" 
                  stroke="#E53E3E" stroke-width="2" stroke-dasharray="4,4"/>
            
            <polyline points="65,65 85,69 105,74 125,82 145,90 165,99 185,110 205,110 225,121" 
                     fill="none" stroke="#E53E3E" stroke-width="1.5"/>
            
            <line x1="225" y1="121" x2="260" y2="33" 
                  stroke="#E53E3E" stroke-width="2" stroke-dasharray="4,4"/>
            

            <g fill="#E53E3E">
              <circle cx="40" cy="59" r="2"/> 
              <circle cx="65" cy="65" r="2"/> 
              <circle cx="85" cy="69" r="2"/> 
              <circle cx="105" cy="74" r="2"/>
              <circle cx="125" cy="82" r="2"/> 
              <circle cx="145" cy="90" r="2"/> 
              <circle cx="165" cy="99" r="2"/> 
              <circle cx="185" cy="110" r="2"/>
              <circle cx="205" cy="110" r="2"/>
              <circle cx="225" cy="121" r="2"/> 
              <circle cx="260" cy="33" r="2"/> 
            </g>
          </svg>
        </div>
      </div>
    </div>
  </body>
</html>