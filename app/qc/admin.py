import boto3
import logging
from flask import redirect, url_for, session
from flask_admin import BaseView, expose
import flask_admin.model.typefmt as typefmt

from flask_login import current_user
from markupsafe import Markup
from werkzeug.utils import secure_filename
from wtforms import File<PERSON>ield, <PERSON><PERSON><PERSON><PERSON><PERSON>

from app import db
from app.dispatch.models import DispatchRecord
from app.message_queue.models import IncomingMessageQueue, OutgoingMessageQueue
from app.qc.models import Lab, Equipment, EquipmentCompany, ControlMethod, Parameter, ControlRule
from app.site.admin_mixins import SiteSpecificAdminMixin
from common import TrialModelView
from config import Config
from botocore.client import Config as BotoConfig

logger = logging.getLogger(__name__)


def linebreak_list_formatter(view, values):
    """Format list values with line breaks instead of raw list."""
    if not values:
        return ""
    return Markup(', <br/>'.join(str(v) for v in values))


EXTENDED_FORMATTERS = dict(typefmt.BASE_FORMATTERS)
EXTENDED_FORMATTERS.update({
    list: linebreak_list_formatter
})


class LabsAdminView(TrialModelView):
    site_id_field = 'site__ref_id'

    column_auto_select_related = True
    column_list = ('name', 'site', 'equipments',)
    can_export = False
    form_columns = ('name', 'site')

    column_type_formatters = EXTENDED_FORMATTERS
    column_labels = {'equipments': 'Equipment'}

    def get_query(self):
        return super().get_query().filter(self.model.site__ref_id == session.get('site_id'))

    def get_count_query(self):
        return super().get_count_query().filter(self.model.site__ref_id == session.get('site_id'))

    def on_model_change(self, form, model, is_created):
        setattr(model, self.site_id_field, session.get('site_id'))
        super().on_model_change(form, model, is_created)


class EquipmentAdminView(TrialModelView):
    column_auto_select_related = True
    page_size = 20  # the number of entries to display on the list view
    column_default_sort = 'model'
    column_list = ('model', 'serial_no', 'lab', 'equipment_manufacturer', 'equipment_distributor', 'purchase_date',
                   'active', 'inactivity_date', 'inactivity_reason', 'control_methods')
    column_filters = ('model', 'serial_no', 'lab.name')
    column_searchable_list = column_filters
    column_labels = {'lab.name': 'Lab Name', }

    can_export = False
    form_columns = ('model', 'serial_no', 'equipment_manufacturer', 'equipment_distributor', 'purchase_date',
                    'active', 'inactivity_date', 'inactivity_reason', 'lab')

    form_args = {
        'lab': {
            'query_factory': lambda: Lab.query.filter(
                Lab.site__ref_id == session.get('site_id')
            ),
        }
    }

    column_type_formatters = EXTENDED_FORMATTERS

    def get_query(self):
        return super().get_query().join(Lab).filter(Lab.site__ref_id == session.get('site_id'))

    def get_count_query(self):
        return super().get_count_query().join(Lab).filter(Lab.site__ref_id == session.get('site_id'))


class EquipmentCompaniesAdminView(SiteSpecificAdminMixin, TrialModelView):
    column_auto_select_related = True
    can_export = False
    form_columns = ('name', 'address', 'manufactured_equipments',
                    'distributed_equipments')


class ControlMethodsAdminView(TrialModelView):
    column_auto_select_related = True
    can_export = False
    column_list = ('control_method_type', 'first_name',
                   'sur_name', 'gender', 'date_of_birth', 'equipments')
    column_labels = {'equipments': 'Related Equipment',
                     'sur_name': 'Surname'}
    column_type_formatters = EXTENDED_FORMATTERS

    form_columns = ('control_method_type', 'first_name', 'sur_name', 'gender', 'date_of_birth' )


class ParametersAdminView(SiteSpecificAdminMixin, TrialModelView):
    column_auto_select_related = True
    can_export = False
    column_list = ('abbreviation', 'long_name',
                   'unit', 'decimal_places', 'site', 'active')
    form_columns = ('abbreviation', 'long_name',
                    'unit', 'decimal_places', 'active', 'inactivity_reason', 'inactivity_date')


class ControlRulesAdminView(TrialModelView):
    can_edit = False
    can_delete = False
    can_create = False
    column_display_actions = False


class ConfigAdminView(BaseView):
    @expose("/")
    def index(self):
        if current_user.has_role('superuser'):
            return self.render('admin/config.html')
        return redirect(url_for('auth_bp.login_view'))


# Set up boto3 client for S3
s3_client = boto3.client(
    "s3",
    aws_access_key_id=Config.AWS_ACCESS_KEY,
    aws_secret_access_key=Config.AWS_SECRET_KEY,
    region_name=Config.AWS_REGION_NAME,
    config=BotoConfig(signature_version='s3v4')
)

S3_BUCKET_NAME = Config.AWS_S3_BUCKET


class EquipmentDocumentsAdminView(TrialModelView):

    # Override save logic to upload to S3
    def _save_file(self, file_data):
        # Ensure the file has content
        if file_data and file_data.filename != '':
            # Secure the filename
            filename = secure_filename(file_data.filename)

            # Upload the file to S3 using boto3's upload_fileobj method
            try:
                s3_client.upload_fileobj(
                    file_data,  # Pass the file object directly
                    S3_BUCKET_NAME,
                    filename,
                    # Make the file publicly readable
                    ExtraArgs={"ACL": "public-read"}
                )
                # Return the S3 file URL
                return f'https://{S3_BUCKET_NAME}.s3.amazonaws.com/{filename}'
            except Exception as e:
                logger.error("Error uploading file: %s", e)
                raise e
        else:
            raise ValueError("No file provided or empty file uploaded")

    # Custom logic to delete file from S3
    def _delete_file_from_s3(self, file_url):
        # Extract the filename from the URL and delete from S3
        filename = file_url.split('/')[-1]
        try:
            s3_client.delete_object(Bucket=S3_BUCKET_NAME, Key=filename)
        except Exception as e:
            logger.error("Error deleting file from S3: %s", e)
            raise e

    # Override on_model_change to handle file upload
    def on_model_change(self, form, model, is_created):
        if form.delete_file.data and model.file_path is not None:
            # Delete the file from S3 if the delete_image checkbox is checked
            self._delete_file_from_s3(form.file_path.object_data)
            model.file_path = None  # Clear the image path
        if form.file_path.data:
            file_data = form.file_path.data
            # Upload to S3 and save the S3 URL in image_path
            model.file_path = self._save_file(file_data)

    # Override edit form to display current file and delete option
    def edit_form(self, obj=None):
        form = super(EquipmentDocumentsAdminView, self).edit_form(obj)
        if obj and obj.file_path:
            # Show the existing image preview
            form.file_path.description = Markup(
                f'''
                <div>
                    <a href="{obj.file_path}" target="_blank">View current file ({obj.file_path})</a>
                </div>
            '''
            )
        return form

    # Customize the form to add an image upload field
    form_extra_fields = {
        'file_path': FileField('Choose Document'), 'delete_file': BooleanField('Delete File'),
        # 'file_path': form.FileUploadField('Choose Document')
    }

    # Custom formatter to show the thumbnail and download link in the list view
    def _list_file_and_download(view, context, model, name):
        if not model.file_path:
            return ''

        # Create a thumbnail and a download link for the File
        return Markup(
            f'''
            <div>
                <a href="{model.file_path}" download>Download</a>
            </div>
        '''
        )

    column_formatters = {
        'download': _list_file_and_download
    }

    column_auto_select_related = True
    can_export = False
    column_list = ('name', 'description', 'equipment', 'download')
    form_columns = ('name', 'description', 'file_path', 'delete_file',
                    'equipment')


class SiteFilteredAdminView(TrialModelView):
    site_id_field = 'site_id'
    can_create = False
    can_edit = False
    can_delete = False
    column_display_actions = False

    def get_query(self):
        return super().get_query().filter(self.model.site_id == session.get('site_id'))

    def get_count_query(self):
        return super().get_count_query().filter(self.model.site_id == session.get('site_id'))


class BaseMessageQueueAdminView(SiteFilteredAdminView):
    column_list = ('id', 'message_id', 'site', 'queue_name', 'payload', 'status', 'created_at', 'updated_at')
    column_filters = ('queue_name', 'status', 'message_id')
    column_searchable_list = ('queue_name', 'status', 'message_id')


class IncomingMessageQueueAdminView(BaseMessageQueueAdminView):
    pass


class OutgoingMessageQueueAdminView(BaseMessageQueueAdminView):
    column_list = ('id', 'message_id', 'site', 'queue_name', 'payload', 'status', 'created_at', 'updated_at', 'attempts', 'error_message')


class IncomingMessageQueueErrorsAdminView(IncomingMessageQueueAdminView):
    def get_query(self):
        return super().get_query().filter(self.model.status == 'failed')

    def get_count_query(self):
        return super().get_count_query().filter(self.model.status == 'failed')


class OutgoingMessageQueueErrorsAdminView(OutgoingMessageQueueAdminView):
    def get_query(self):
        return super().get_query().filter(self.model.status == 'failed')

    def get_count_query(self):
        return super().get_count_query().filter(self.model.status == 'failed')


class DispatchRecordAdminView(SiteFilteredAdminView):
    column_list = ('id', 'message_id', 'site', 'source', 'status', 'stage', 'created_at', 'updated_at', 'filename', 'provider_number', 'fax_number', 'ur', 'failure_reason', 'is_resolved', 'attempts')
    column_filters = ('message_id', 'source', 'status', 'stage', 'filename', 'provider_number', 'fax_number', 'ur')
    column_searchable_list = ('message_id', 'source', 'status', 'stage', 'filename', 'provider_number', 'fax_number', 'ur')


def add_admins(admin):
    admin.add_view(LabsAdminView(Lab, db.session, category='QC', name='Labs'))
    admin.add_view(EquipmentAdminView(Equipment, db.session, category='QC'))
    admin.add_view(
        EquipmentCompaniesAdminView(
            EquipmentCompany, db.session, category='QC'
        )
    )
    admin.add_view(
        ControlMethodsAdminView(
            ControlMethod, db.session, category='QC', name='Control Methods',
        )
    )
    admin.add_view(
        ParametersAdminView(
            Parameter, db.session, category='QC', name='Parameters',
        )
    )
    admin.add_view(
        ControlRulesAdminView(
            ControlRule, db.session, category='QC', name='Control Rules',
        )
    )
    admin.add_view(
        IncomingMessageQueueAdminView(
            IncomingMessageQueue, db.session, category='Integration', name='Incoming Messages'
        )
    )
    admin.add_view(
        OutgoingMessageQueueAdminView(
            OutgoingMessageQueue, db.session, category='Integration', name='Outgoing Messages'
        )
    )
    admin.add_view(
        IncomingMessageQueueErrorsAdminView(
            IncomingMessageQueue, db.session, category='Integration', name='Incoming Message Errors', endpoint='incoming-message-errors'
        )
    )
    admin.add_view(
        OutgoingMessageQueueErrorsAdminView(
            OutgoingMessageQueue, db.session, category='Integration', name='Outgoing Message Errors', endpoint='outgoing-message-errors'
        )
    )
    admin.add_view(
        DispatchRecordAdminView(
            DispatchRecord, db.session, category='Integration', name='Dispatch Records'
        )
    )
    admin.add_view(
        ConfigAdminView(
            name='Relationship Configuration',
            endpoint='config', category='QC'
        )
    )
