/*! tab icons */
.e-tab .e-tab-header .e-toolbar-items.e-hscroll.e-rtl .e-nav-left-arrow::before {
  content: '\e219';
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll.e-rtl .e-nav-right-arrow::before {
  content: '\e98f';
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-left-arrow::before {
  content: '\e98f';
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  content: '\e219';
}

.e-tab .e-tab-header .e-close-icon::before {
  content: '\e7fc';
  position: relative;
}

.e-tab .e-tab-header .e-popup-up-icon::before {
  content: '\e85e';
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-popup-up-icon::before {
    content: '\e976';
  }
}

.e-tab .e-tab-header .e-popup-down-icon::before {
  content: '\e84f';
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-popup-down-icon::before {
    content: '\e976';
  }
}

@media screen and (max-width: 480px) {
  .e-tab.e-vertical-icon .e-tab-header .e-popup-up-icon::before {
    content: '\e85e';
  }
  .e-tab.e-vertical-icon .e-tab-header .e-popup-down-icon::before {
    content: '\e84f';
  }
}

.e-tab.e-vertical-tab .e-tab-header .e-scroll-nav .e-nav-up-arrow::before {
  content: '\ebbb';
}

.e-tab.e-vertical-tab .e-tab-header .e-scroll-nav .e-nav-down-arrow::before {
  content: '\ebbd';
}

.e-tab.e-vertical-tab .e-tab-header .e-popup-up-icon::before {
  content: 'More';
}

.e-tab.e-vertical-tab .e-tab-header .e-popup-up-icon::after {
  content: '\ebb8';
}

.e-tab.e-vertical-tab .e-tab-header .e-popup-down-icon::before {
  content: 'More';
}

.e-tab.e-vertical-tab .e-tab-header .e-popup-down-icon::after {
  content: '\ebb8';
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-up-icon::before {
  content: '';
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-up-icon::after {
  content: '\ebb7';
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-down-icon::before {
  content: '';
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header .e-popup-down-icon::after {
  content: '\ebb7';
}

/*! tab layout */
.e-bigger .e-tab .e-tab-header,
.e-tab.e-bigger .e-tab-header {
  height: 50px;
  min-height: 50px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items,
.e-tab.e-bigger .e-tab-header .e-toolbar-items {
  height: auto;
  min-height: auto;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0 14px 0 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
  .e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0 8px 0 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child,
  .e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active,
.e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  margin: 0;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-items.e-hscroll .e-scroll-nav,
.e-tab.e-bigger .e-tab-header .e-toolbar-items.e-hscroll .e-scroll-nav {
  width: 50px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 50px;
  margin: 0 14px 0 0;
  min-height: 50px;
  min-width: auto;
  padding: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator),
  .e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator) {
    margin: 0 8px 0 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator).e-itop, .e-bigger .e-tab .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom,
.e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator).e-itop,
.e-tab.e-bigger .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom {
  height: 72px;
  min-height: 72px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 50px;
  padding: 0 8px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-wrap {
    padding: 0 8px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-text-wrap {
  height: 50px;
  height: 46px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-text {
  font-size: 15px;
  -ms-flex-item-align: auto;
      -ms-grid-row-align: auto;
      align-self: auto;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active {
  margin: 0 14px 0 0;
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active {
    margin: 0 8px 0 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 46px;
  margin-top: 0;
  height: 46px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 50px;
  height: 46px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap {
  padding: 2px 0 0 0;
  padding: 0;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
  cursor: pointer;
  margin: 0 0 0 8px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
    margin: 0 0 0 8px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-close-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-close-icon::before {
  font-size: 12px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-close-icon::before,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-close-icon,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-close-icon::before {
    font-size: 12px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icons.e-tab-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icons.e-tab-icon {
  height: 20px;
  min-width: 20px;
  width: 20px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-icon,
.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-tab-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-tab-icon::before {
  font-size: 20px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap {
  padding: 0 8px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
  margin: 0 0 0 8px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text,
  .e-tab.e-bigger .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
    margin: 0 0 0 8px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom {
  height: 72px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 72px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap {
  height: 72px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon {
  right: 8px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-text-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap {
  height: 72px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-itop .e-tab-text {
  margin: 10px 0 0;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0 0 10px;
}

.e-bigger .e-tab .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-bigger .e-tab .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-right: 22px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item {
  height: 45px;
  min-height: 45px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
  height: 45px;
  padding: 0 16px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
    padding: 0 12px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-text-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-text-wrap {
  height: 45px;
  height: 41px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'],
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'] {
  padding-right: 8px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'],
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'] {
    padding-right: 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon {
  margin: 0;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon::before,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon::before {
  top: 1px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  right: 16px;
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator),
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  min-height: 50px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator),
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
    min-height: 50px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop,
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom,
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 50px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop,
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom,
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
    height: 50px;
  }
}

.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
  padding-left: 0;
  padding-right: 12px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-bigger .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-tab.e-bigger .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
  }
}

.e-bigger .e-tab .e-tab-header .e-scroll-nav,
.e-tab.e-bigger .e-tab-header .e-scroll-nav {
  height: 50px;
  min-height: 50px;
  padding: 0 10px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-scroll-nav,
  .e-tab.e-bigger .e-tab-header .e-scroll-nav {
    padding: 0 8px;
  }
}

.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-scroll-right-nav,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-scroll-right-nav {
  padding: 0 10px;
}

.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-scroll-left-nav,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-scroll-left-nav {
  padding: 0 10px;
}

.e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-arrow,
.e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-arrow {
  font-size: 14px;
  height: 32px;
  line-height: 32px;
  width: 32px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-arrow,
  .e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-arrow {
    font-size: 14px;
    height: 32px;
    line-height: 32px;
    width: 32px;
  }
}

.e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-bigger .e-tab .e-tab-header .e-scroll-nav .e-nav-right-arrow::before,
.e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab.e-bigger .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  font-size: 14px;
  line-height: 32px;
  top: 0;
  vertical-align: initial;
  top: -2px;
}

.e-bigger .e-tab .e-tab-header .e-hor-nav,
.e-tab.e-bigger .e-tab-header .e-hor-nav {
  height: 50px;
  min-height: 50px;
  padding: 0 10px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-hor-nav,
  .e-tab.e-bigger .e-tab-header .e-hor-nav {
    padding: 0 8px;
  }
}

.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon {
  font-size: 14px;
  height: 32px;
  line-height: 32px;
  width: 32px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon {
    font-size: 14px;
    height: 32px;
    line-height: 32px;
    width: 32px;
  }
}

.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon::before {
  font-size: 14px;
  line-height: 32px;
  top: 0;
  vertical-align: initial;
  top: -2px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 16px;
  }
}

.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover::before,
.e-bigger .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-up-icon:hover::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav .e-popup-down-icon:hover::before {
  top: 0;
  top: -2px;
}

.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon::before,
.e-bigger .e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-up-icon::before,
.e-tab.e-bigger .e-tab-header .e-hor-nav:focus .e-popup-down-icon::before {
  top: 0;
  top: -2px;
}

.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  line-height: 20px;
}

.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  line-height: 17px;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  margin: 0;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin: 0 14px 0 0;
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active,
  .e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
    margin: 0 8px 0 0;
  }
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-text {
  padding-top: 0;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child {
  margin: 0;
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child,
  .e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:last-child {
    margin: 0;
  }
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop, .e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom {
  padding-bottom: 0;
  padding-top: 0;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-icon-close::before,
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-icon-close::before,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-icon-close::before,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-icon-close::before {
  top: -1px;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ileft.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ileft.e-active .e-text-wrap {
  margin: 0;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 50px;
  padding: 0;
  height: 46px;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-text-wrap,
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ibottom .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ibottom .e-text-wrap {
  height: 50px;
  height: auto;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap,
.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap {
  height: initial;
}

.e-bigger .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ileft.e-active,
.e-tab.e-bigger .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ileft.e-active {
  height: 46px;
}

.e-bigger .e-tab .e-tab-header.e-vertical,
.e-tab.e-bigger .e-tab-header.e-vertical {
  max-width: 150px;
}

.e-bigger .e-tab .e-tab-header.e-vertical[style*='overflow: hidden']::before,
.e-tab.e-bigger .e-tab-header.e-vertical[style*='overflow: hidden']::before {
  bottom: 23px;
  top: 23px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab .e-tab-header.e-vertical[style*='overflow: hidden']::before,
  .e-tab.e-bigger .e-tab-header.e-vertical[style*='overflow: hidden']::before {
    bottom: 0;
    top: 0;
  }
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-items,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-items.e-vscroll:not(.e-scroll-device),
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-items.e-vscroll:not(.e-scroll-device) {
  padding: 24px 0;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-item .e-tab-wrap {
  padding: 0 8px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon {
  right: -22px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ileft.e-icon,
.e-tab.e-bigger .e-tab-header.e-vertical .e-toolbar-item.e-ileft.e-icon {
  min-height: 50px;
  min-width: 50px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-scroll-nav,
.e-tab.e-bigger .e-tab-header.e-vertical .e-scroll-nav {
  height: 24px;
  min-height: 24px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow,
.e-tab.e-bigger .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  width: 24px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 8px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  line-height: 48px;
}

.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-bigger .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab.e-bigger .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  font-size: 15px;
  line-height: 48px;
  margin: 0 0 0 8px;
}

.e-bigger .e-tab .e-tab-header.e-vertical.e-toolpop .e-toolbar-items,
.e-tab.e-bigger .e-tab-header.e-vertical.e-toolpop .e-toolbar-items {
  height: auto;
}

.e-bigger .e-tab.e-vertical-icon .e-tab-header,
.e-tab.e-bigger.e-vertical-icon .e-tab-header {
  height: 72px;
  min-height: 72px;
}

.e-bigger .e-tab.e-vertical-icon .e-tab-header .e-toolbar-items,
.e-tab.e-bigger.e-vertical-icon .e-tab-header .e-toolbar-items {
  height: 72px;
}

.e-bigger .e-tab.e-vertical-icon .e-tab-header .e-toolbar-item.e-active .e-text-wrap::before,
.e-bigger .e-tab.e-vertical-icon .e-tab-header .e-toolbar-item:not(.e-separator):last-child.e-active .e-text-wrap::before,
.e-tab.e-bigger.e-vertical-icon .e-tab-header .e-toolbar-item.e-active .e-text-wrap::before,
.e-tab.e-bigger.e-vertical-icon .e-tab-header .e-toolbar-item:not(.e-separator):last-child.e-active .e-text-wrap::before {
  bottom: -10px;
  top: auto;
}

.e-bigger .e-tab.e-vertical-icon .e-tab-header .e-scroll-nav,
.e-tab.e-bigger.e-vertical-icon .e-tab-header .e-scroll-nav {
  height: 72px;
}

.e-bigger .e-tab.e-vertical-icon .e-tab-header .e-hor-nav,
.e-tab.e-bigger.e-vertical-icon .e-tab-header .e-hor-nav {
  height: 72px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-bigger .e-tab.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-down-icon::before,
  .e-tab.e-bigger.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab.e-bigger.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 12px;
  }
}

.e-bigger .e-tab.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-toolbar-items,
.e-tab.e-bigger.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}

.e-bigger .e-tab.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-scroll-nav,
.e-tab.e-bigger.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-scroll-nav {
  height: 24px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 50px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
  height: 46px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus, .e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 71px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus {
  height: 45px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 45px;
  height: 41px;
}

.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-bigger .e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-bigger.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 50px;
}

.e-bigger .e-tab.e-focused .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-focused .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
  padding: 0;
  height: 46px;
}

.e-bigger .e-tab.e-focused .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ileft,
.e-tab.e-bigger.e-focused .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ileft {
  height: 46px;
}

.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  line-height: 20px;
}

.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  line-height: 17px;
}

.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  line-height: 18px;
}

.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-bigger .e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-bigger.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  line-height: 13px;
}

.e-bigger .e-tab.e-safari .e-tab-header .e-close-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-close-icon::before {
  top: -1px;
}

.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-up-icon::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-down-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-popup-up-icon::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-popup-down-icon::before {
  top: 0;
}

.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-left-arrow::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-right-arrow::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-bigger .e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-right-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-nav-left-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-hor-nav .e-nav-right-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab.e-bigger.e-safari .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  top: 0;
}

.e-bigger .e-tab.e-fill .e-tab-header,
.e-tab.e-bigger.e-fill .e-tab-header {
  height: 48px;
  min-height: 48px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header,
  .e-tab.e-bigger.e-fill .e-tab-header {
    height: 48px;
    min-height: 48px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items {
  height: auto;
  min-height: 48px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items {
    min-height: 48px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 48px;
  margin: 0;
  min-height: 48px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator),
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
    height: 48px;
    min-height: 48px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-itop, .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-itop,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom {
  height: 70px;
  min-height: 70px;
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 48px;
  padding: 0 10px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
    height: 48px;
    padding: 0 10px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
  height: 48px;
  height: 44px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
    height: 48px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 48px;
  height: 44px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
    height: 48px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 48px;
  height: 44px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
  .e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
  .e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
    height: 48px;
  }
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap,
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 70px;
}

.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-bigger .e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator),
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-tab.e-bigger.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  height: auto;
}

.e-bigger .e-tab.e-fill .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 8px;
}

.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 46px;
  height: 44px;
}

.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap,
.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-bigger .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: 50px;
}

.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 48px;
}

.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 48px;
  height: 44px;
}

.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap {
  height: 44px;
}

.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-close-icon::before,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-close-icon::before {
  top: 1px;
}

.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 69px;
}

.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
  .e-bigger .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap,
  .e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
  .e-tab.e-bigger.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
    height: auto;
  }
}

.e-bigger .e-tab.e-fill.e-focused .e-tab-header.e-horizontal-bottom .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-bigger.e-fill.e-focused .e-tab-header.e-horizontal-bottom .e-tab-wrap:focus .e-text-wrap {
  height: 44px;
}

.e-bigger .e-tab.e-fill.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-fill.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-bigger .e-tab.e-fill.e-vertical-icon .e-tab-header,
.e-tab.e-bigger.e-fill.e-vertical-icon .e-tab-header {
  height: 70px;
  min-height: 70px;
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
  padding: 0 12px;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap,
  .e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
    padding: 0 12px;
  }
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item.e-active,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 50px;
  height: 46px;
}

.e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item:hover.e-itop .e-text-wrap, .e-bigger .e-tab.e-background .e-tab-header .e-toolbar-item:hover.e-ibottom .e-text-wrap,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item:hover.e-itop .e-text-wrap,
.e-tab.e-bigger.e-background .e-tab-header .e-toolbar-item:hover.e-ibottom .e-text-wrap {
  height: auto;
}

.e-bigger .e-tab.e-background .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap,
.e-tab.e-bigger.e-background .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 8px;
}

.e-bigger .e-tab.e-background.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-background.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0 0 0 14px;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 14px;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text {
  margin: 0 8px 0 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text {
    margin: 0 8px 0 0;
  }
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon {
  margin: 0 8px 0 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon {
    margin: 0 8px 0 0;
  }
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-itop .e-close-icon,
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-itop .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon {
  left: 8px;
  right: auto;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-bigger .e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap,
.e-tab.e-bigger.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-bigger.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-left: 22px;
  margin-right: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item {
  margin: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
  padding-left: 8px;
  padding-right: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
    padding-left: 0;
  }
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-icons.e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-icons.e-close-icon {
  margin: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  left: 16px;
  right: auto;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
  padding-left: 12px;
  padding-right: 0;
}

@media screen and (max-width: 480px) {
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-bigger .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-tab.e-bigger.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
  }
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-hor-nav,
.e-bigger .e-tab.e-rtl .e-tab-header .e-scroll-right-nav,
.e-tab.e-bigger.e-rtl .e-tab-header .e-hor-nav,
.e-tab.e-bigger.e-rtl .e-tab-header .e-scroll-right-nav {
  padding: 0 10px;
}

.e-bigger .e-tab.e-rtl .e-tab-header .e-scroll-left-nav,
.e-tab.e-bigger.e-rtl .e-tab-header .e-scroll-left-nav {
  padding: 0 10px;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator),
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon, .e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon {
  left: -22px;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 8px;
}

.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-popup-up-icon::after,
.e-bigger .e-tab.e-rtl .e-tab-header.e-vertical .e-popup-down-icon::after,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-popup-up-icon::after,
.e-tab.e-bigger.e-rtl .e-tab-header.e-vertical .e-popup-down-icon::after {
  margin: 0 8px 0 0;
}

.e-tab {
  display: block;
  position: relative;
}

.e-tab.e-hidden {
  display: none;
}

.e-tab .e-tab-header {
  height: 40px;
  min-height: 40px;
  -webkit-user-select: text;
     -moz-user-select: text;
      -ms-user-select: text;
          user-select: text;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header {
    height: 50px;
    min-height: 50px;
  }
}

.e-tab .e-tab-header::before {
  content: '';
  position: absolute;
}

.e-tab .e-tab-header:not(.e-vertical)::before {
  bottom: 0;
  top: 0;
  width: 100%;
}

.e-tab .e-tab-header .e-toolbar-items {
  height: auto;
  margin: 0;
  min-height: 40px;
  position: relative;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-items {
    min-height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0 8px 0 0;
}

.e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0;
  }
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll.e-scroll-device {
  padding: 0;
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll.e-scroll-device .e-scroll-right-nav {
  display: none;
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll .e-hscroll-bar {
  padding: 0 1px;
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll .e-scroll-nav {
  width: 40px;
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0;
  }
}

.e-tab .e-tab-header .e-toolbar-items.e-hscroll:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  margin: 0;
}

.e-tab .e-tab-header .e-hscroll-bar {
  overflow: hidden;
}

.e-tab .e-tab-header .e-indicator {
  display: none;
  position: absolute;
}

.e-tab .e-tab-header .e-indicator.e-hidden {
  display: none;
}

.e-tab .e-tab-header:not(.e-vertical) .e-indicator {
  bottom: 0;
  height: 2px;
  left: 0;
  right: 0;
  transition: left 0.125s cubic-bezier(0.35, 0, 0.25, 1), right 0.25s cubic-bezier(0.35, 0, 0.25, 1);
}

.e-tab .e-tab-header .e-toolbar-item {
  -webkit-user-select: text;
     -moz-user-select: text;
      -ms-user-select: text;
          user-select: text;
}

.e-tab .e-tab-header .e-toolbar-item.e-hidden {
  display: none;
}

.e-tab .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 40px;
  margin: 0 8px 0 0;
  min-height: 40px;
  min-width: auto;
  padding: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item:not(.e-separator) {
    height: 50px;
    min-height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 40px;
  padding: 0 8px;
  width: 100%;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap {
    height: 50px;
    padding: 0 8px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-text-wrap {
  -ms-flex-line-pack: center;
      align-content: center;
  -ms-flex-align: center;
      align-items: center;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 40px;
  height: 36px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-text-wrap {
    height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-text {
  display: inherit;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-text {
    font-size: 15px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-text {
  -ms-flex-item-align: auto;
      -ms-grid-row-align: auto;
      align-self: auto;
}

.e-tab .e-tab-header .e-toolbar-item.e-active {
  margin: 0 8px 0 0;
  padding-bottom: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-active {
    margin: 0 8px 0 0;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 36px;
  margin-top: 0;
  height: 36px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
    height: 46px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-text {
  font-weight: 600;
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-text {
  -ms-flex-item-align: auto;
      -ms-grid-row-align: auto;
      align-self: auto;
}

.e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 40px;
  padding: 2px 0 0 0;
  height: 36px;
  padding: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
    height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-tab-wrap .e-tab-text, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-tab-wrap .e-tab-text {
  -ms-flex-item-align: auto;
      -ms-grid-row-align: auto;
      align-self: auto;
}

.e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-tab-wrap:focus .e-tab-text, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-tab-wrap:focus .e-tab-text {
  -ms-flex-item-align: auto;
      -ms-grid-row-align: auto;
      align-self: auto;
}

.e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
  display: none;
  margin: 0 0 0 8px;
  min-width: 10px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-icons.e-close-icon {
    margin: 0 0 0 8px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-close-icon,
.e-tab .e-tab-header .e-toolbar-item .e-close-icon::before {
  font-size: 10px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-close-icon,
  .e-tab .e-tab-header .e-toolbar-item .e-close-icon::before {
    font-size: 12px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-close-icon::before {
  top: 1px;
}

@media screen\0 {
  .e-tab .e-tab-header .e-toolbar-item .e-close-icon::before {
    top: 1px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-icons.e-tab-icon {
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 20px;
  min-width: 20px;
  width: 20px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-icons.e-tab-icon {
    height: 20px;
    width: 20px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-icon,
.e-tab .e-tab-header .e-toolbar-item .e-tab-icon::before {
  font-size: 18px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-icon,
  .e-tab .e-tab-header .e-toolbar-item .e-tab-icon::before {
    font-size: 20px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap {
  -ms-flex-pack: center;
      justify-content: center;
  padding: 0 8px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-icon .e-tab-wrap {
    padding: 0 8px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
  margin: 0 0 0 6px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-icon-left + .e-tab-text {
    margin: 0 0 0 8px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-item.e-ibottom {
  height: 62px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-item.e-ibottom {
    height: 72px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  height: 62px;
  position: relative;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
    height: 72px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab .e-tab-header .e-toolbar-item.e-itop .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-text-wrap {
  display: block;
  height: auto;
}

.e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap {
  height: 62px;
}

.e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap .e-text-wrap, .e-tab .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap .e-text-wrap {
  height: auto;
}

.e-tab .e-tab-header .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon {
  position: absolute;
  right: 8px;
  top: calc(50% - 6px);
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-close-icon {
    right: 8px;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-text {
  margin: 8px 0 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-itop .e-tab-text {
    margin: 10px 0 0;
  }
}

.e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0 0 8px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0 0 10px;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-icon.e-icon-top,
.e-tab .e-tab-header .e-toolbar-item .e-tab-icon.e-icon-bottom {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-pack: center;
      justify-content: center;
  margin: auto;
}

.e-tab .e-tab-header .e-toolbar-pop {
  overflow-y: auto;
  padding: 0;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item {
  height: 36px;
  min-height: 36px;
  min-width: auto;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item {
    height: 45px;
    min-height: 45px;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
  height: 36px;
  padding: 0 16px;
  text-align: initial;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap {
    height: 45px;
    padding: 0 12px;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-text-wrap {
  height: 36px;
  width: 100%;
  height: 32px;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 100%;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon {
  margin: 0 0 0 8px;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon::before {
  top: 1px;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'] {
  padding-right: 8px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text + .e-close-icon[style='display:block'] {
    padding-right: 0;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom {
  height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom {
    height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  min-height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator), .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
    min-height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 40px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap {
    height: 50px;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-text-wrap, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-text-wrap {
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: auto;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  display: block;
  -ms-flex-pack: center;
      justify-content: center;
  margin: 0 0 0 10px;
  padding-left: 0;
  padding-right: 10px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    -ms-flex-pack: center;
        justify-content: center;
    margin: 0 0 0 12px;
    padding-left: 0;
    padding-right: 0;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  right: 16px;
  top: initial;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-icon.e-icon-top,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-icon.e-icon-bottom {
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.e-tab .e-tab-header.e-close-show .e-icons.e-close-icon {
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-item-align: center;
      align-self: center;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-close-show .e-icons.e-close-icon {
    display: none;
  }
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-close-show .e-toolbar-item.e-active .e-close-icon {
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}

.e-tab .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-right: 20px;
}

.e-tab .e-tab-header .e-scroll-nav {
  height: 40px;
  min-height: 40px;
  min-width: auto;
  width: auto;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-scroll-nav {
    height: 50px;
    min-height: 50px;
  }
}

.e-tab .e-tab-header .e-scroll-nav.e-scroll-left-nav {
  padding: 0 10px;
}

.e-tab .e-tab-header .e-scroll-nav.e-scroll-right-nav {
  padding: 0 10px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-scroll-nav.e-scroll-right-nav {
    padding: 0 8px;
  }
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  font-size: 12px;
  line-height: 24px;
  position: relative;
  top: 0;
  vertical-align: initial;
  top: -2px;
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow {
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  width: 24px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-scroll-nav .e-nav-arrow {
    font-size: 14px;
    height: 32px;
    line-height: 32px;
    width: 32px;
  }
}

.e-tab .e-tab-header .e-hor-nav {
  height: 40px;
  min-height: 40px;
  min-width: auto;
  padding: 0 10px;
  width: auto;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav {
    height: 50px;
    min-height: 50px;
    padding: 0 8px;
  }
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  width: 24px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
  .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
    font-size: 14px;
    height: 32px;
    line-height: 32px;
    width: 32px;
  }
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before {
  font-size: 12px;
  line-height: 24px;
  position: relative;
  top: 0;
  vertical-align: initial;
  top: -2px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 16px;
  }
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover::before,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover::before {
  top: 0;
  top: -2px;
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-up-icon {
    transform: none;
    transition: none;
  }
}

.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-hor-nav .e-popup-down-icon {
    transform: none;
    transition: none;
  }
}

.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon::before,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon::before {
  top: 0;
  top: -2px;
}

.e-tab .e-tab-header.e-horizontal-bottom::before {
  bottom: auto;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding-bottom: 0;
  padding-top: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
    margin: 0;
  }
}

.e-tab .e-tab-header.e-horizontal-bottom .e-indicator {
  bottom: auto;
  top: 0;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin: 0 8px 0 0;
  padding-bottom: 0;
  padding-top: 0;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
    margin: 0 8px 0 0;
  }
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 40px;
  padding: 0 0 2px 0;
  height: 36px;
  padding: 0;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap::before,
.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active:not(.e-separator):last-child .e-text-wrap::before {
  top: 0;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-tab-wrap:focus .e-close-icon::before {
  top: 0;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-itop .e-text-wrap {
  height: auto;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ileft .e-tab-wrap .e-tab-text {
  -ms-flex-item-align: auto;
      -ms-grid-row-align: auto;
      align-self: auto;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active.e-ileft .e-tab-wrap:focus .e-tab-text {
  -ms-flex-item-align: auto;
      -ms-grid-row-align: auto;
      align-self: auto;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap, .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap {
  height: initial;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap, .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap {
  height: initial;
  padding: 0;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-text-wrap::before, .e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-text-wrap::before {
  bottom: auto;
  top: 0;
}

.e-tab .e-tab-header.e-vertical {
  max-width: 150px;
  z-index: 1;
}

.e-tab .e-tab-header.e-vertical::before {
  bottom: 0;
  height: 100%;
  left: 0;
  top: 0;
}

.e-tab .e-tab-header.e-vertical[style*='overflow: hidden']::before {
  bottom: 15px;
  height: auto;
  top: 15px;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-vertical[style*='overflow: hidden']::before {
    bottom: 0;
    top: 0;
  }
}

.e-tab .e-tab-header.e-vertical .e-indicator {
  display: block;
  transition: top 0.125s cubic-bezier(0.35, 0, 0.25, 1), bottom 0.25s cubic-bezier(0.35, 0, 0.25, 1);
  width: 2px;
  transition: initial;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-items.e-vscroll:not(.e-scroll-device) {
  padding: 16px 0;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item:last-child {
  margin: 0;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-wrap {
  padding: 0 8px;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-text-wrap {
  position: relative;
  width: 100%;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-text,
.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-icon::before {
  text-align: center;
  width: 100%;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item .e-tab-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-active .e-text-wrap::before {
  display: none;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ileft.e-icon {
  min-height: 40px;
  min-width: 40px;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ileft.e-icon .e-tab-icon {
  margin: auto;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-itop .e-close-icon, .e-tab .e-tab-header.e-vertical .e-toolbar-item.e-ibottom .e-close-icon {
  right: -20px;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-pop {
  top: initial !important;
}

.e-tab .e-tab-header.e-vertical.e-vertical-left {
  float: left;
}

.e-tab .e-tab-header.e-vertical.e-vertical-left::before {
  right: 0;
}

.e-tab .e-tab-header.e-vertical.e-vertical-left .e-indicator {
  left: auto;
  right: 0;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right {
  float: right;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right::before {
  right: auto;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right .e-indicator {
  left: 0;
  right: auto;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right .e-tab-wrap {
  text-align: right;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right .e-toolbar-pop .e-tab-text {
  width: auto;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav {
  height: 16px;
  -ms-flex-pack: center;
      justify-content: center;
  min-height: 16px;
  width: 100%;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  margin: 0 auto;
  width: 16px;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 8px;
  width: 100%;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon {
  height: 100%;
  transform: none;
  transition: none;
  width: 100%;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  float: left;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  font-weight: 400;
  line-height: 36px;
  text-align: left;
  text-transform: capitalize;
  transform: none;
  transition: none;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  float: left;
  font-size: 12px;
  line-height: 36px;
  margin: 0 0 0 8px;
  vertical-align: initial;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
  .e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
    font-size: 15px;
  }
}

.e-tab .e-tab-header.e-vertical .e-scroll-device .e-scroll-nav,
.e-tab .e-tab-header.e-vertical .e-scroll-device .e-scroll-overlay {
  display: none;
}

.e-tab .e-tab-header.e-vertical.e-toolpop .e-toolbar-items {
  height: auto;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator).e-itop .e-close-icon,
.e-tab.e-rtl .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon {
  left: -20px;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav {
  padding: 0 8px;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  float: right;
}

.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab.e-rtl .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  float: right;
  margin: 0 8px 0 0;
}

.e-tab .e-content {
  position: relative;
}

.e-tab .e-content .e-item.e-view {
  bottom: 0;
  left: 0;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
}

.e-tab .e-content > .e-item {
  display: none;
}

.e-tab .e-content > .e-item.e-active {
  background: inherit;
  display: block;
}

.e-tab .e-content.e-progress {
  overflow-x: hidden;
}

.e-tab.e-vertical-tab .e-content {
  display: -ms-flexbox;
  display: flex;
}

.e-tab.e-vertical-tab .e-content .e-item.e-active {
  display: -ms-flexbox;
  display: flex;
  -ms-flex: none;
      flex: none;
}

.e-tab.e-vertical-tab .e-content .e-item,
.e-tab.e-vertical-tab .e-content .e-item > :first-child {
  width: 100%;
}

.e-tab.e-vertical-icon .e-tab-header {
  height: 62px;
  min-height: 62px;
}

.e-tab.e-vertical-icon .e-tab-header .e-toolbar-items {
  height: 62px;
}

.e-tab.e-vertical-icon .e-tab-header .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-vertical-icon .e-tab-header .e-toolbar-item:not(.e-separator):last-child.e-active .e-text-wrap {
  position: static;
}

.e-tab.e-vertical-icon .e-tab-header .e-toolbar-item.e-active .e-text-wrap::before,
.e-tab.e-vertical-icon .e-tab-header .e-toolbar-item:not(.e-separator):last-child.e-active .e-text-wrap::before {
  content: none;
}

.e-tab.e-vertical-icon .e-tab-header .e-toolbar-item.e-active .e-tab-wrap::before,
.e-tab.e-vertical-icon .e-tab-header .e-toolbar-item:not(.e-separator):last-child.e-active .e-tab-wrap::before {
  content: '';
  display: block;
  position: absolute;
  top: 96%;
  width: calc(100% - 16px);
  border: 1px solid #400074;
}

.e-tab.e-vertical-icon .e-tab-header .e-scroll-nav {
  height: 62px;
}

.e-tab.e-vertical-icon .e-tab-header .e-hor-nav {
  height: 62px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-up-icon::before,
  .e-tab.e-vertical-icon .e-tab-header .e-hor-nav .e-popup-down-icon::before {
    font-size: 12px;
  }
}

.e-tab.e-vertical-icon .e-tab-header.e-vertical .e-indicator {
  width: 3px;
}

.e-tab.e-vertical-icon .e-tab-header.e-vertical .e-toolbar-item.e-active .e-tab-wrap::before,
.e-tab.e-vertical-icon .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator):last-child.e-active .e-tab-wrap::before {
  display: none;
}

.e-tab.e-vertical-icon .e-tab-header.e-vertical .e-toolbar-item.e-active .e-text-wrap,
.e-tab.e-vertical-icon .e-tab-header.e-vertical .e-toolbar-item:not(.e-separator):last-child.e-active .e-text-wrap {
  position: relative;
}

.e-tab.e-vertical-icon .e-tab-header.e-vertical .e-scroll-nav {
  height: 16px;
}

.e-tab.e-vertical-icon .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap::before,
.e-tab.e-vertical-icon .e-tab-header.e-horizontal-bottom .e-toolbar-item:not(.e-separator):last-child.e-active .e-tab-wrap::before {
  top: 0;
}

.e-tab.e-vertical-icon.e-vertical-tab .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 40px;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 40px;
  height: 36px;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus, .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 62px;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus {
  height: 36px;
}

.e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 36px;
  height: 32px;
}

.e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-focused .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 40px;
}

.e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap {
  height: 40px;
  padding: 0;
  height: 36px;
}

.e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-active.e-ileft .e-tab-wrap:focus .e-tab-text {
  -ms-flex-item-align: auto;
      -ms-grid-row-align: auto;
      align-self: auto;
}

.e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-itop.e-active .e-tab-wrap:focus .e-text-wrap, .e-tab.e-focused .e-tab-header .e-horizontal-bottom .e-toolbar-item.e-ibottom.e-active .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

.e-tab.e-template .e-content > .e-item {
  display: none;
}

.e-tab.e-template .e-content > .e-item.e-active {
  background: inherit;
  display: block;
}

.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align {
  display: -ms-flexbox;
  display: flex;
}

.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  display: block;
  line-height: 15px;
}

.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-ie .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-ie .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  display: block;
  line-height: 15px;
}

.e-tab.e-ie .e-tab-header .e-popup-up-icon,
.e-tab.e-ie .e-tab-header .e-popup-down-icon {
  transform: none;
  transition: none;
}

.e-tab.e-ie .e-tab-header .e-popup-up-icon::before {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-ie .e-tab-header .e-popup-up-icon::before {
    transform: none;
    transition: none;
  }
}

.e-tab.e-ie .e-tab-header .e-popup-down-icon::before {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-ie .e-tab-header .e-popup-down-icon::before {
    transform: none;
    transition: none;
  }
}

.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align {
  display: -ms-flexbox;
  display: flex;
}

.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-popup-down-icon,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-up-icon,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-popup-down-icon {
  display: block;
  line-height: 14px;
  position: relative;
}

.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-edge .e-tab-header .e-hor-nav.e-ie-align .e-nav-right-arrow,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-left-arrow,
.e-tab.e-edge .e-tab-header .e-scroll-nav.e-ie-align .e-nav-right-arrow {
  display: block;
  line-height: 12px;
  position: relative;
}

.e-tab.e-edge .e-tab-header .e-popup-up-icon {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-up-icon {
    transform: none;
    transition: none;
  }
}

.e-tab.e-edge .e-tab-header .e-popup-down-icon {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-down-icon {
    transform: none;
    transition: none;
  }
}

@media screen and (max-width: 480px) and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-down-icon {
    transform: none;
    transition: none;
  }
}

.e-tab.e-edge .e-tab-header .e-popup-up-icon::before {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-up-icon::before {
    transform: none;
    transition: none;
  }
}

.e-tab.e-edge .e-tab-header .e-popup-down-icon::before {
  transform: rotate(0deg);
  transition: none;
}

@media screen and (max-width: 480px) {
  .e-tab.e-edge .e-tab-header .e-popup-down-icon::before {
    transform: none;
    transition: none;
  }
}

.e-tab.e-safari .e-tab-header .e-close-icon::before {
  top: -1px;
}

.e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-safari .e-tab-header .e-hor-nav .e-popup-down-icon::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-up-icon::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-popup-down-icon::before {
  top: 0;
}

.e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-left-arrow::before,
.e-tab.e-safari .e-tab-header .e-hor-nav .e-nav-right-arrow::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-left-arrow::before,
.e-tab.e-safari .e-tab-header .e-scroll-nav .e-nav-right-arrow::before {
  top: 0;
}

.e-tab.e-disable {
  pointer-events: none;
}

.e-tab.e-fill .e-tab-header {
  height: 38px;
  min-height: 38px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header {
    height: 48px;
    min-height: 48px;
  }
}

.e-tab.e-fill .e-tab-header .e-indicator {
  display: none;
}

.e-tab.e-fill .e-tab-header .e-toolbar-items {
  height: auto;
  min-height: 38px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-items {
    min-height: 48px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-fill .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
  height: 38px;
  margin: 0;
  min-height: 38px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator) {
    height: 48px;
    min-height: 48px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-itop, .e-tab.e-fill .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom {
  height: 60px;
  min-height: 60px;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
  height: 38px;
  padding: 0 10px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
    height: 48px;
    padding: 0 10px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
  height: 38px;
  height: 34px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
    height: 48px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 38px;
  height: 34px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
    height: 48px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-text {
  font-weight: 600;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
  height: 38px;
  height: 34px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-ileft .e-text-wrap,
  .e-tab.e-fill .e-tab-header .e-toolbar-item.e-active.e-iright .e-text-wrap {
    height: 48px;
  }
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap {
  height: 60px;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-ibottom .e-text-wrap {
  height: auto;
}

.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop:not(.e-separator),
.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom:not(.e-separator) {
  height: auto;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child.e-active, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  padding: 0;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item.e-itop, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child.e-itop, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-itop {
  padding-top: 0;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin-right: 0;
  padding: 0;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  height: 38px;
  height: 34px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
    height: 48px;
  }
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: 44px;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-itop .e-text-wrap, .e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-ibottom .e-text-wrap {
  height: auto;
}

.e-tab.e-fill .e-tab-header.e-vertical .e-toolbar-items {
  height: inherit;
}

.e-tab.e-fill .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 8px;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  height: 38px;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-text-wrap {
  height: 38px;
  height: 34px;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-text-wrap {
  height: 34px;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-close-icon::before {
  top: 1px;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus {
  height: 59px;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
  height: auto;
}

@media screen and (max-width: 480px) {
  .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-itop .e-tab-wrap:focus .e-text-wrap,
  .e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-ibottom .e-tab-wrap:focus .e-text-wrap {
    height: auto;
  }
}

.e-tab.e-fill.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-tab.e-fill.e-vertical-icon .e-tab-header {
  height: 60px;
  min-height: 60px;
}

.e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-background .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0;
  padding: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item:hover.e-itop .e-text-wrap,
.e-tab.e-background .e-tab-header .e-toolbar-item:hover.e-ibottom .e-text-wrap {
  height: auto;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
  padding: 0 10px;
}

@media screen and (max-width: 480px) {
  .e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
    padding: 0 12px;
  }
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active {
  padding: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  margin-bottom: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  height: 40px;
  height: 36px;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-text {
  font-weight: 600;
}

.e-tab.e-background .e-tab-header.e-vertical .e-indicator {
  display: none;
}

.e-tab.e-background .e-tab-header.e-vertical .e-toolbar-items .e-toolbar-item .e-tab-wrap {
  padding: 0 8px;
}

.e-tab.e-background .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child.e-active,
.e-tab.e-background .e-tab-header.e-horizontal-bottom .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child.e-active {
  padding: 0;
}

.e-tab.e-background .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  margin: 0;
  padding: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item.e-active .e-text-wrap {
  height: 36px;
  height: 32px;
}

.e-tab.e-background.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:first-child,
.e-tab.e-rtl .e-tab-header .e-toolbar-items:not(.e-tbar-pos) .e-toolbar-item:last-child {
  margin: 0 0 0 8px;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) {
  margin: 0 0 0 8px;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icon-left + .e-tab-text {
  margin: 0 6px 0 0;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator) .e-icons.e-close-icon {
  margin: 0 8px 0 0;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-itop .e-close-icon,
.e-tab.e-rtl .e-tab-header .e-toolbar-item:not(.e-separator).e-ibottom .e-close-icon {
  left: 8px;
  right: auto;
}

.e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-itop .e-text-wrap,
.e-tab.e-rtl .e-tab-header.e-close-show .e-toolbar-item.e-ibottom .e-text-wrap {
  margin-left: 20px;
  margin-right: 0;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item {
  margin: 0;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
  padding-left: 8px;
  padding-right: 0;
}

@media screen and (max-width: 480px) {
  .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-text {
    padding-left: 0;
  }
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-icons.e-close-icon {
  margin: 0;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item .e-close-icon {
  left: 16px;
  right: auto;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-close-icon,
.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-close-icon {
  left: 16px;
  right: auto;
}

.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
.e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
  margin: 0;
  padding-left: 10px;
  padding-right: 0;
}

@media screen and (max-width: 480px) {
  .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-itop .e-tab-text,
  .e-tab.e-rtl .e-tab-header .e-toolbar-pop .e-toolbar-item.e-ibottom .e-tab-text {
    margin: 0;
    padding-left: 10px;
    padding-right: 0;
  }
}

.e-tab.e-rtl .e-tab-header .e-hor-nav,
.e-tab.e-rtl .e-tab-header .e-scroll-right-nav {
  padding: 0 10px;
}

.e-tab.e-rtl .e-tab-header .e-scroll-left-nav {
  padding: 0 10px;
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::before,
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::before {
  display: none;
}

.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon::after,
.e-tab.e-vertical-tab.e-icon-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon::after {
  margin: 0;
  width: 100%;
}

/*! tab theme */
.e-tab {
  background: #fff;
  border: 1px solid #757575;
}

.e-tab .e-tab-header {
  background: inherit;
  border: 0;
  border-radius: 0;
  border-bottom: 0;
}

.e-tab .e-tab-header:not(.e-vertical) .e-toolbar-item.e-active {
  border-bottom: initial;
}

.e-tab .e-tab-header .e-toolbar-items {
  background: inherit;
}

.e-tab .e-tab-header .e-indicator {
  background: #400074;
}

.e-tab .e-tab-header .e-toolbar-item {
  background: inherit;
  border: none;
}

.e-tab .e-tab-header .e-toolbar-item .e-ripple-element {
  background: rgba(255, 255, 255, 0);
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap {
  border-radius: 0;
  color: #000;
  border: 2px solid #fff;
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-icon {
  color: #000;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap {
    color: #000;
  }
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-icon {
    color: #000;
  }
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap .e-close-icon {
    color: #000;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover {
  background: inherit;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover {
    background: inherit;
  }
}

.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-icon {
  color: #000;
}

.e-tab .e-tab-header .e-toolbar-item.e-active {
  border: 0;
  border-radius: 0;
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-icon {
  color: #000;
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  position: relative;
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-text-wrap::before {
  border: 1px solid #400074;
  content: '';
  display: block;
  position: absolute;
  top: 95%;
  width: 100%;
}

.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #000;
}

.e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-tab-icon, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-tab-icon {
  color: #000;
}

.e-tab .e-tab-header .e-toolbar-item.e-active.e-ileft .e-tab-text, .e-tab .e-tab-header .e-toolbar-item.e-active.e-iright .e-tab-text {
  color: #000;
}

.e-tab .e-tab-header .e-toolbar-item .e-close-icon {
  color: #000;
}

.e-tab .e-tab-header .e-toolbar-item .e-close-icon:hover {
  color: #400074;
}

.e-tab .e-tab-header .e-toolbar-item .e-close-icon:active {
  color: #400074;
}

.e-tab .e-tab-header .e-toolbar-item.e-disable.e-overlay {
  background: inherit;
  opacity: 1;
  pointer-events: none;
}

.e-tab .e-tab-header .e-toolbar-item.e-disable.e-overlay .e-tab-text,
.e-tab .e-tab-header .e-toolbar-item.e-disable.e-overlay .e-tab-icon {
  color: #757575;
}

.e-tab .e-tab-header .e-toolbar-pop {
  background: #fff;
  border: 1px solid #757575;
  border-radius: 0;
  box-shadow: none;
  overflow-y: auto;
}

.e-tab .e-tab-header .e-toolbar-pop[e-animate='true'] {
  overflow-y: hidden;
}

@media screen and (max-width: 480px) {
  .e-tab .e-tab-header .e-toolbar-pop {
    box-shadow: none;
  }
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-text,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-icon {
  color: #000;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover {
  background: #ecf;
  border-color: #000;
  background: #ecf;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-icon,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-close-icon {
  color: #000;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active {
  background: #400074;
  border-color: #400074;
}

.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active .e-tab-text,
.e-tab .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active .e-tab-icon {
  color: #fff;
}

.e-tab .e-tab-header .e-scroll-nav,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav {
  background: inherit;
  border: 0;
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow {
  color: #000;
  border: 2px solid #fff;
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow:hover,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow:hover {
  background: #ecf;
  color: #000;
  border: 2px solid #000;
}

.e-tab .e-tab-header .e-scroll-nav .e-nav-arrow:active,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav .e-nav-arrow:active {
  box-shadow: none;
}

.e-tab .e-tab-header .e-scroll-nav:focus .e-nav-arrow,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-nav-arrow {
  background: transparent;
  color: #000;
  border: 2px solid #000;
}

.e-tab .e-tab-header .e-scroll-nav:focus .e-nav-arrow:hover,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-nav-arrow:hover {
  background: #ecf;
  color: #000;
  border: 2px solid #000;
}

.e-tab .e-tab-header .e-scroll-nav:focus .e-nav-arrow:active,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav:focus .e-nav-arrow:active {
  background: #400074;
  border-color: #400074;
  color: #fff;
}

.e-tab .e-tab-header .e-scroll-nav.e-overlay .e-nav-arrow,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav.e-overlay .e-nav-arrow {
  color: #000;
}

.e-tab .e-tab-header .e-scroll-nav.e-overlay .e-nav-arrow:hover,
.e-tab .e-tab-header .e-hscroll:not(.e-scroll-device) .e-scroll-nav.e-overlay .e-nav-arrow:hover {
  color: #000;
  background: transparent;
  border-color: transparent;
}

.e-tab .e-tab-header .e-hor-nav,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) {
  background: transparent;
  border: 0;
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon {
  color: #000;
  border: 2px solid #fff;
}

.e-tab .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon:hover {
  background: #ecf;
  border: initial;
  border-radius: 0;
  border: 2px solid #000;
}

.e-tab .e-tab-header .e-hor-nav .e-popup-down-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav) .e-popup-down-icon:hover {
  color: #000;
}

.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon {
  background: transparent;
  border: initial;
  border-radius: 0;
  color: #000;
  border: 2px solid #000;
}

.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon:hover {
  background: #ecf;
  border: initial;
  border-radius: 0;
  border: 2px solid #000;
}

.e-tab .e-tab-header .e-hor-nav:focus .e-popup-up-icon:active,
.e-tab .e-tab-header .e-hor-nav:focus .e-popup-down-icon:active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-up-icon:active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):focus .e-popup-down-icon:active {
  background: #ecf;
  border: 0;
  border-radius: 0;
  box-shadow: none;
  background: #400074;
  border: 2px solid #400074;
  color: #fff;
}

.e-tab .e-tab-header .e-hor-nav:hover,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):hover {
  border: 0;
}

.e-tab .e-tab-header .e-hor-nav:hover:active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav):hover:active {
  background: transparent;
}

.e-tab .e-tab-header .e-hor-nav.e-nav-active,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav).e-nav-active {
  background: inherit;
  border: 0;
  box-shadow: none;
}

.e-tab .e-tab-header .e-hor-nav.e-nav-active .e-icons,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav).e-nav-active .e-icons {
  color: #000;
}

.e-tab .e-tab-header .e-hor-nav.e-nav-active:focus,
.e-tab .e-tab-header .e-hor-nav:not(.e-expended-nav).e-nav-active:focus {
  border: 0;
}

.e-tab .e-tab-header.e-horizontal-bottom {
  border-bottom: 0;
  border-top: 0;
}

.e-tab .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active {
  border-bottom: 0;
  border-radius: 0;
  border-top: initial;
  border-top-color: inherit;
}

.e-tab .e-tab-header.e-vertical::before {
  border-width: 0 1px 0 0;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-item.e-active {
  border-bottom: 0;
  border-top: 0;
}

.e-tab .e-tab-header.e-vertical.e-vertical-left .e-toolbar-item.e-active {
  border-radius: 0;
  border-right: initial;
}

.e-tab .e-tab-header.e-vertical.e-vertical-right .e-toolbar-item.e-active {
  border-left: initial;
  border-radius: 0;
}

.e-tab .e-tab-header.e-vertical .e-toolbar-pop {
  box-shadow: none;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav {
  background: inherit;
  border: 1px solid transparent;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav.e-scroll-up-nav {
  border-bottom-color: #000;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav.e-scroll-down-nav {
  border-top-color: #000;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  border: 0;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow:hover {
  background: none;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:hover {
  background: #ecf;
  border-color: #000;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:hover:active:focus {
  background: #400074;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:hover .e-nav-arrow {
  color: #000;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:hover .e-nav-arrow:hover {
  background: none;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:focus {
  background: transparent;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:focus:hover:active .e-nav-arrow {
  color: #fff;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow {
  background: none;
}

.e-tab .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow:hover {
  background: none;
  border: 0;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon {
  border: 0;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon:hover,
.e-tab .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon:hover {
  background: inherit;
  border: 0;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon {
  background: inherit;
  border: 0;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:hover, .e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:active, .e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:active:hover,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:hover,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:active,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:active:hover {
  background: inherit;
  border: 0;
  box-shadow: none;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:active,
.e-tab .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:active {
  color: #000;
}

.e-tab .e-tab-header.e-vertical .e-hor-nav:hover .e-popup-up-icon,
.e-tab .e-tab-header.e-vertical .e-hor-nav:hover .e-popup-down-icon {
  border: 0;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  background: initial;
  border-color: #000;
  border-style: solid;
}

.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon,
.e-tab.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text {
  color: #000;
}

.e-tab.e-focused .e-tab-header .e-scroll-nav:focus .e-nav-arrow {
  color: #000;
  background: inherit;
  border: 2px solid #000;
}

.e-tab.e-focused .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-focused .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  color: #000;
  background: inherit;
  border: 2px solid #000;
}

.e-tab.e-focused .e-tab-header.e-vertical .e-scroll-nav:focus {
  border-color: #000;
}

.e-tab.e-focused .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow {
  border: 0;
}

.e-tab.e-focused .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon,
.e-tab.e-focused .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon {
  border: 0;
}

.e-tab.e-focused .e-tab-header.e-vertical .e-hor-nav:focus {
  outline: 1.5px solid #000;
}

.e-tab.e-focused .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-focused .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon {
  border-color: #000;
}

.e-tab.e-disable {
  pointer-events: none;
}

.e-tab.e-disable .e-tab-header .e-indicator {
  background: #757575;
}

.e-tab.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #757575;
}

.e-tab.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #757575;
}

.e-tab.e-disable .e-content {
  opacity: 0.38;
}

.e-tab.e-fill .e-tab-header {
  border-bottom: 2px Solid #400074;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item {
  border: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-ripple-element {
  background: rgba(255, 255, 255, 0);
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #000;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab.e-fill .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-icon {
  color: #000;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item .e-text-wrap {
  margin-top: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active {
  border: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  background: #400074;
  border-radius: 0;
  border-color: #400074;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-icon {
  color: #fff;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap {
  margin-top: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-text-wrap::before {
  border: 0;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #fff;
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-close-icon {
  color: rgba(255, 255, 255, 0.6);
}

.e-tab.e-fill .e-tab-header .e-toolbar-item.e-active .e-close-icon:hover {
  color: #fff;
}

.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item:active:hover .e-tab-text,
.e-tab.e-fill .e-tab-header .e-toolbar-pop .e-toolbar-item:active:hover .e-tab-icon {
  color: #fff;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom {
  border-bottom: 0;
  border-top: 2px Solid #400074;
}

.e-tab.e-fill .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-text-wrap {
  margin-top: 0;
}

.e-tab.e-fill .e-tab-header.e-vertical {
  border-bottom: 0;
}

.e-tab.e-fill .e-tab-header.e-vertical.e-vertical-left {
  border-right: 2px Solid #400074;
}

.e-tab.e-fill .e-tab-header.e-vertical.e-vertical-right {
  border-left: 2px Solid #400074;
}

.e-tab.e-fill.e-corner .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  border-radius: 3px 3px 0 0;
}

.e-tab.e-fill.e-corner .e-tab-header.e-horizontal-bottom .e-toolbar-item.e-active .e-tab-wrap {
  border-radius: 0 0 3px 3px;
}

.e-tab.e-fill.e-disable .e-tab-header {
  border-bottom: 2px Solid #757575;
}

.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #757575;
}

.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active {
  opacity: 1;
}

.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #757575;
}

.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-text,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-icon,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-fill.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #4f4f4f;
}

.e-tab.e-fill.e-disable .e-tab-header.e-horizontal-bottom {
  border-bottom: 0;
  border-top: 2px Solid #757575;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-close-icon {
  color: #000;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #400074;
  border-color: #000;
  border-style: solid;
}

.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-fill.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header {
  background: #ccc;
  border: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-items {
  background: #ccc;
}

.e-tab.e-background .e-tab-header .e-indicator {
  background: #400074;
}

.e-tab.e-background .e-tab-header .e-toolbar-item {
  border: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-ripple-element {
  background: rgba(255, 255, 255, 0);
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap {
  background: #ccc;
  border-color: #ccc;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover {
  background: inherit;
  background-color: #ecf;
  border-color: #000;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-icon {
  color: #fff;
  color: #000;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-close-icon {
  color: #000;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-close-icon:hover, .e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-wrap:hover .e-close-icon:active {
  color: #400074;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-item .e-close-icon {
  color: #000;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-close-icon {
  color: #000;
}

.e-tab.e-background .e-tab-header .e-toolbar-item .e-close-icon:hover {
  color: #000;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active {
  border: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap {
  background: #400074;
  border-radius: 0;
  border-color: #400074;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover {
  background: #ecf;
  border-color: #000;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-close-icon {
  color: #000;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-close-icon:hover, .e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover .e-close-icon:active {
  color: #400074;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover:focus {
  background: #ecf;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover:focus .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:hover:focus .e-tab-text {
  color: #000;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #400074;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-text-wrap::before {
  border: 0;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-tab-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-close-icon {
  color: rgba(255, 255, 255, 0.6);
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active .e-close-icon:hover {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-active.e-itop .e-tab-wrap,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-active.e-ibottom .e-tab-wrap {
  background: #400074;
}

.e-tab.e-background .e-tab-header .e-toolbar-item.e-disable .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-disable .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-item.e-disable .e-close-icon {
  color: #757575;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item {
  background: #ccc;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap .e-tab-icon {
  color: #000;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover {
  background: #ecf;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-tab-icon,
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:hover .e-close-icon {
  color: #fff;
  color: #000;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active {
  background: #400074;
}

.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active .e-tab-text,
.e-tab.e-background .e-tab-header .e-toolbar-pop .e-toolbar-item .e-tab-wrap:active .e-close-icon {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-scroll-nav .e-nav-arrow {
  color: #000;
  border: 2px solid #ccc;
}

.e-tab.e-background .e-tab-header .e-scroll-nav .e-nav-arrow:hover {
  background: #ecf;
  color: #000;
  border-color: #000;
  color: #000;
}

.e-tab.e-background .e-tab-header .e-scroll-nav .e-nav-arrow:active {
  background: #400074;
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-scroll-nav .e-nav-arrow:active .e-icons {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-scroll-nav:focus .e-nav-arrow {
  color: #000;
  background: transparent;
  border: 2px solid #000;
}

.e-tab.e-background .e-tab-header .e-scroll-nav:focus .e-nav-arrow:hover {
  background: #ecf;
  color: #000;
  border: 2px solid #000;
  color: #000;
}

.e-tab.e-background .e-tab-header .e-scroll-nav:focus .e-nav-arrow:active {
  background: #400074;
  border-color: #400074;
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-scroll-nav:active::after {
  animation: none;
}

.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-up-icon,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-down-icon {
  color: #000;
  border: 2px solid #ccc;
}

.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-down-icon:hover {
  background: #ecf;
  color: #000;
  border-color: #000;
  color: #000;
}

.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-up-icon:active .e-icons,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-down-icon:active .e-icons {
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-up-icon:active:hover,
.e-tab.e-background .e-tab-header .e-hor-nav .e-popup-down-icon:active:hover {
  background: #400074;
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  color: #000;
  background: transparent;
  border: 2px solid #000;
}

.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-down-icon:hover {
  background: #ecf;
  border: 2px solid #000;
  color: #000;
}

.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-up-icon:active,
.e-tab.e-background .e-tab-header .e-hor-nav:focus .e-popup-down-icon:active {
  background: #400074;
  border-color: #400074;
  color: #fff;
}

.e-tab.e-background .e-tab-header .e-hor-nav:active::after {
  animation: none;
}

.e-tab.e-background .e-tab-header .e-hor-nav.e-nav-active .e-popup-up-icon {
  color: #000;
}

.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav .e-nav-arrow {
  border: 0;
}

.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow {
  border: 0;
}

.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav:focus .e-nav-arrow:hover {
  background: none;
}

.e-tab.e-background .e-tab-header.e-vertical .e-scroll-nav:hover .e-nav-arrow {
  color: #000;
}

.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon {
  background: inherit;
  border-color: transparent;
}

.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav:focus .e-popup-down-icon:hover {
  background: inherit;
  border-color: transparent;
}

.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav .e-popup-up-icon:hover,
.e-tab.e-background .e-tab-header.e-vertical .e-hor-nav .e-popup-down-icon:hover {
  background: inherit;
  border-color: transparent;
}

.e-tab.e-background.e-disable .e-tab-header .e-indicator {
  background: #757575;
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap {
  color: #757575;
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap .e-tab-icon {
  color: #757575;
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon {
  color: #757575;
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active {
  opacity: 1;
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  background: #757575;
  color: #4f4f4f;
}

.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap .e-tab-icon,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-disable .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #4f4f4f;
}

.e-tab.e-background.e-accent .e-tab-header .e-indicator {
  background: #400074;
}

.e-tab.e-background.e-accent .e-tab-header.e-disable .e-indicator {
  background: #757575;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus {
  border-color: #000;
  border-style: solid;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-icon,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item .e-tab-wrap:focus .e-close-icon {
  color: #000;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus {
  border-color: #000;
  border-style: solid;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-text,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active .e-tab-wrap:focus .e-tab-icon {
  color: #fff;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-itop .e-tab-wrap,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-ibottom .e-tab-wrap {
  background: #400074;
}

.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-itop .e-tab-wrap:focus,
.e-tab.e-background.e-focused .e-tab-header .e-toolbar-item.e-active.e-ibottom .e-tab-wrap:focus {
  background: transparent;
}

.e-tab.e-background.e-focused .e-tab-header .e-scroll-nav:focus .e-nav-arrow {
  color: #000;
}

.e-tab.e-background.e-focused .e-tab-header .e-hor-nav:focus .e-popup-up-icon,
.e-tab.e-background.e-focused .e-tab-header .e-hor-nav:focus .e-popup-down-icon {
  color: #000;
}

.e-tab.e-rtl .e-tab-header .e-hscroll.e-rtl .e-scroll-nav {
  border: 0;
}

.e-tab .e-content {
  background: inherit;
}

.e-tab .e-content .e-item {
  background: inherit;
  color: #000;
}
