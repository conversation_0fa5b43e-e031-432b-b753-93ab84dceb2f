@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
/*! contextmenu layout */
.e-contextmenu-wrapper ul .e-menu-item .e-previous::before {
  content: '\e977';
}

.e-contextmenu-wrapper ul .e-menu-item .e-caret::before {
  content: '\e956';
}

.e-rtl.e-contextmenu-wrapper .e-menu-item .e-caret::before {
  content: '\e937';
}

/*! contextmenu layout */
.e-contextmenu-wrapper ul {
  font-weight: normal;
  list-style-image: none;
  list-style-position: outside;
  list-style-type: none;
  margin: 0;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  font-size: 14px;
  padding: 8px 0;
  border: none;
  border-radius: 0;
  box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.24);
  display: none;
  min-width: 120px;
  position: absolute;
}

.e-contextmenu-wrapper ul.e-ul,
.e-contextmenu-wrapper ul.e-ul * {
  box-sizing: border-box;
}

.e-contextmenu-wrapper ul.e-ul:focus,
.e-contextmenu-wrapper ul.e-ul *:focus {
  outline: none;
}

.e-contextmenu-wrapper ul.e-contextmenu {
  box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.26);
}

.e-contextmenu-wrapper ul.e-ul {
  font-family: "Roboto", "Segoe UI", "GeezaPro", "DejaVu Serif", "sans-serif", "-apple-system", "BlinkMacSystemFont";
}

.e-contextmenu-wrapper ul .e-menu-item {
  cursor: pointer;
  position: relative;
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
}

.e-contextmenu-wrapper ul .e-menu-item.e-menu-hide {
  display: none;
}

.e-contextmenu-wrapper ul .e-menu-item.e-menu-header {
  border-bottom-style: solid;
  border-bottom-width: 1px;
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-url {
  text-decoration: none;
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-icon {
  display: inline-block;
  vertical-align: middle;
}

.e-contextmenu-wrapper ul .e-menu-item.e-separator {
  cursor: auto;
  line-height: normal;
  pointer-events: none;
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-url {
  display: inline-block;
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-icon {
  font-size: 14px;
  line-height: 36px;
  margin-right: 10px;
}

.e-contextmenu-wrapper ul .e-menu-item .e-caret {
  line-height: 36px;
  margin-left: 16px;
  margin-right: 0;
  position: absolute;
  right: 8px;
}

.e-contextmenu-wrapper ul .e-menu-item.e-menu-caret-icon {
  padding-right: 36px;
}

.e-contextmenu-wrapper ul .e-menu-item.e-separator {
  border-bottom-style: solid;
  border-bottom-width: 1px;
  height: auto;
  margin: 8px 0;
}

.e-contextmenu-wrapper ul .e-menu-item.e-blankicon {
  padding-left: 40px;
}

.e-contextmenu-wrapper ul .e-menu-item .e-caret {
  font-size: 12px;
}

.e-contextmenu-wrapper ul .e-menu-item .e-previous {
  margin-right: 16px;
}

.e-contextmenu-wrapper ul .e-menu-item.e-disabled {
  cursor: auto;
  pointer-events: none;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item .e-menu-icon {
  float: right;
  margin-right: 0;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item .e-caret {
  margin-left: 0;
  right: auto;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item .e-menu-icon {
  margin-left: 10px;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item .e-caret {
  left: 8px;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item.e-menu-caret-icon {
  padding-left: 36px;
  padding-right: 16px;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item.e-blankicon {
  padding-left: 16px;
  padding-right: 40px;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item.e-blankicon.e-menu-caret-icon {
  padding-left: 36px;
}

.e-bigger .e-contextmenu-wrapper ul,
.e-bigger.e-contextmenu-wrapper ul {
  font-size: 15px;
  padding: 8px 0;
  white-space: nowrap;
  box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.26);
  max-width: 280px;
  min-width: 112px;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item,
.e-bigger.e-contextmenu-wrapper ul .e-menu-item {
  height: 48px;
  line-height: 48px;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item .e-menu-icon,
.e-bigger.e-contextmenu-wrapper ul .e-menu-item .e-menu-icon {
  font-size: 16px;
  line-height: 48px;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item .e-caret,
.e-bigger.e-contextmenu-wrapper ul .e-menu-item .e-caret {
  line-height: 48px;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item.e-separator,
.e-bigger.e-contextmenu-wrapper ul .e-menu-item.e-separator {
  height: auto;
  line-height: normal;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item.e-blankicon,
.e-bigger.e-contextmenu-wrapper ul .e-menu-item.e-blankicon {
  padding-left: 42px;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item .e-caret,
.e-bigger.e-contextmenu-wrapper ul .e-menu-item .e-caret {
  font-size: 12px;
}

.e-bigger .e-contextmenu-wrapper.e-rtl ul .e-blankicon,
.e-bigger.e-contextmenu-wrapper.e-rtl ul .e-blankicon {
  padding-left: 16px;
  padding-right: 42px;
}

.e-bigger .e-contextmenu-wrapper.e-rtl ul .e-blankicon.e-menu-caret-icon,
.e-bigger.e-contextmenu-wrapper.e-rtl ul .e-blankicon.e-menu-caret-icon {
  padding-left: 36px;
}

/*! contextmenu theme */
.e-contextmenu-wrapper ul {
  background-color: #424242;
  color: #fff;
}

.e-contextmenu-wrapper ul .e-menu-item.e-menu-header {
  border-bottom-color: #616161;
}

.e-contextmenu-wrapper ul .e-menu-item .e-caret {
  color: rgba(255, 255, 255, 0.7);
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-icon {
  color: rgba(255, 255, 255, 0.7);
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-url {
  color: #fff;
}

.e-contextmenu-wrapper ul .e-menu-item.e-focused {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  outline: 0 solid #616161;
  outline-offset: 0;
}

.e-contextmenu-wrapper ul .e-menu-item.e-focused .e-caret {
  color: rgba(255, 255, 255, 0.7);
}

.e-contextmenu-wrapper ul .e-menu-item.e-focused .e-menu-icon {
  color: rgba(255, 255, 255, 0.7);
}

.e-contextmenu-wrapper ul .e-menu-item.e-selected {
  background-color: rgba(255, 255, 255, 0.18);
  color: #fff;
  outline: 0 solid rgba(255, 255, 255, 0.18);
  outline-offset: 0;
}

.e-contextmenu-wrapper ul .e-menu-item.e-selected .e-caret {
  color: #fff;
}

.e-contextmenu-wrapper ul .e-menu-item.e-selected .e-menu-icon {
  color: #fff;
}

.e-contextmenu-wrapper ul .e-disabled {
  color: rgba(255, 255, 255, 0.3);
  opacity: 1;
}

.e-contextmenu-wrapper ul .e-disabled .e-menu-icon {
  color: rgba(255, 255, 255, 0.3);
}

.e-contextmenu-wrapper ul .e-disabled .e-caret {
  color: rgba(255, 255, 255, 0.3);
}

.e-contextmenu-wrapper ul .e-disabled .e-menu-url {
  color: rgba(255, 255, 255, 0.3);
}

.e-contextmenu-wrapper ul .e-separator {
  border-bottom-color: #616161;
}
