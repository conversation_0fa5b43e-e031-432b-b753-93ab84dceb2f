/*! contextmenu layout */
.e-contextmenu-wrapper ul .e-menu-item .e-previous::before {
  content: '\e962';
}

.e-contextmenu-wrapper ul .e-menu-item .e-caret::before {
  content: '\e219';
}

.e-rtl.e-contextmenu-wrapper .e-menu-item .e-caret::before {
  content: '\e98f';
}

/*! contextmenu layout */
.e-contextmenu-wrapper ul {
  font-weight: normal;
  list-style-image: none;
  list-style-position: outside;
  list-style-type: none;
  margin: 0;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  font-size: 14px;
  padding: 0;
  border: 1px solid #757575;
  border-radius: 0;
  box-shadow: 0 0 10px 0 rgba(255, 255, 255, 0.2);
  display: none;
  min-width: 120px;
  position: absolute;
}

.e-contextmenu-wrapper ul.e-ul, .e-contextmenu-wrapper ul.e-ul * {
  box-sizing: border-box;
}

.e-contextmenu-wrapper ul.e-ul:focus, .e-contextmenu-wrapper ul.e-ul *:focus {
  outline: none;
}

.e-contextmenu-wrapper ul.e-contextmenu {
  box-shadow: 0 0 10px 0 rgba(255, 255, 255, 0.2);
}

.e-contextmenu-wrapper ul.e-ul {
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
}

.e-contextmenu-wrapper ul .e-menu-item {
  cursor: pointer;
  position: relative;
  height: 36px;
  line-height: 36px;
  padding: 0 10px;
}

.e-contextmenu-wrapper ul .e-menu-item.e-menu-hide {
  display: none;
}

.e-contextmenu-wrapper ul .e-menu-item.e-menu-header {
  border-bottom-style: solid;
  border-bottom-width: 1px;
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-url {
  text-decoration: none;
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-icon {
  display: inline-block;
  vertical-align: middle;
}

.e-contextmenu-wrapper ul .e-menu-item.e-separator {
  cursor: auto;
  line-height: normal;
  pointer-events: none;
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-url {
  display: inline-block;
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-icon {
  font-size: 16px;
  line-height: 36px;
  margin-right: 10px;
}

.e-contextmenu-wrapper ul .e-menu-item .e-caret {
  line-height: 36px;
  margin-left: 16px;
  margin-right: 0;
  position: absolute;
  right: 5px;
}

.e-contextmenu-wrapper ul .e-menu-item.e-menu-caret-icon {
  padding-right: 36px;
}

.e-contextmenu-wrapper ul .e-menu-item.e-separator {
  border-bottom-style: solid;
  border-bottom-width: 1px;
  height: auto;
  margin: 0;
}

.e-contextmenu-wrapper ul .e-menu-item.e-blankicon {
  padding-left: 36px;
}

.e-contextmenu-wrapper ul .e-menu-item .e-caret {
  font-size: 12px;
}

.e-contextmenu-wrapper ul .e-menu-item .e-previous {
  margin-right: 12px;
}

.e-contextmenu-wrapper ul .e-menu-item.e-disabled {
  cursor: auto;
  pointer-events: none;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item .e-menu-icon {
  float: right;
  margin-right: 0;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item .e-caret {
  margin-left: 0;
  right: auto;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item .e-menu-icon {
  margin-left: 10px;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item .e-caret {
  left: 5px;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item.e-menu-caret-icon {
  padding-left: 36px;
  padding-right: 10px;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item.e-blankicon {
  padding-left: 10px;
  padding-right: 36px;
}

.e-rtl.e-contextmenu-wrapper .e-menu-item.e-blankicon.e-menu-caret-icon {
  padding-left: 36px;
}

.e-bigger .e-contextmenu-wrapper ul, .e-bigger.e-contextmenu-wrapper ul {
  font-size: 15px;
  padding: 0;
  white-space: nowrap;
  box-shadow: 0 0 10px 0 rgba(255, 255, 255, 0.2);
  max-width: 280px;
  min-width: 112px;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item, .e-bigger.e-contextmenu-wrapper ul .e-menu-item {
  height: 48px;
  line-height: 48px;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item .e-menu-icon, .e-bigger.e-contextmenu-wrapper ul .e-menu-item .e-menu-icon {
  font-size: 18px;
  line-height: 48px;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item .e-caret, .e-bigger.e-contextmenu-wrapper ul .e-menu-item .e-caret {
  line-height: 48px;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item.e-separator, .e-bigger.e-contextmenu-wrapper ul .e-menu-item.e-separator {
  height: auto;
  line-height: normal;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item.e-blankicon, .e-bigger.e-contextmenu-wrapper ul .e-menu-item.e-blankicon {
  padding-left: 38px;
}

.e-bigger .e-contextmenu-wrapper ul .e-menu-item .e-caret, .e-bigger.e-contextmenu-wrapper ul .e-menu-item .e-caret {
  font-size: 12px;
}

.e-bigger .e-contextmenu-wrapper.e-rtl ul .e-blankicon, .e-bigger.e-contextmenu-wrapper.e-rtl ul .e-blankicon {
  padding-left: 10px;
  padding-right: 38px;
}

.e-bigger .e-contextmenu-wrapper.e-rtl ul .e-blankicon.e-menu-caret-icon, .e-bigger.e-contextmenu-wrapper.e-rtl ul .e-blankicon.e-menu-caret-icon {
  padding-left: 36px;
}

/*! contextmenu theme */
.e-contextmenu-wrapper ul {
  background-color: #fff;
  color: #000;
}

.e-contextmenu-wrapper ul .e-menu-item.e-menu-header {
  border-bottom-color: #757575;
}

.e-contextmenu-wrapper ul .e-menu-item .e-caret {
  color: #000;
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-icon {
  color: #000;
}

.e-contextmenu-wrapper ul .e-menu-item .e-menu-url {
  color: #000;
}

.e-contextmenu-wrapper ul .e-menu-item.e-focused {
  background-color: #ecf;
  color: #000;
  outline: 1px solid #757575;
  outline-offset: -1px;
}

.e-contextmenu-wrapper ul .e-menu-item.e-focused .e-caret {
  color: #000;
}

.e-contextmenu-wrapper ul .e-menu-item.e-focused .e-menu-icon {
  color: #000;
}

.e-contextmenu-wrapper ul .e-menu-item.e-selected {
  background-color: #400074;
  color: #fff;
  outline: 0 solid #400074;
  outline-offset: 0;
}

.e-contextmenu-wrapper ul .e-menu-item.e-selected .e-caret {
  color: #fff;
}

.e-contextmenu-wrapper ul .e-menu-item.e-selected .e-menu-icon {
  color: #fff;
}

.e-contextmenu-wrapper ul .e-disabled {
  color: #757575;
  opacity: 1;
}

.e-contextmenu-wrapper ul .e-disabled .e-menu-icon {
  color: #757575;
}

.e-contextmenu-wrapper ul .e-disabled .e-caret {
  color: #757575;
}

.e-contextmenu-wrapper ul .e-disabled .e-menu-url {
  color: #757575;
}

.e-contextmenu-wrapper ul .e-separator {
  border-bottom-color: #757575;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
