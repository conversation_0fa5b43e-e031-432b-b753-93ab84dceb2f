/*! component icons */
.e-time-wrapper .e-time-icon.e-icons::before, .e-control.e-control-wrapper.e-time-wrapper .e-time-icon.e-icons::before {
  content: '\e97f';
}

.e-input-group.e-control-wrapper.e-time-wrapper.e-non-edit.e-input-focus .e-input:focus ~ .e-clear-icon, .e-float-input.e-control-wrapper.e-input-group.e-time-wrapper.e-non-edit.e-input-focus input:focus ~ .e-clear-icon {
  display: -ms-flexbox;
  display: flex;
}

.e-time-wrapper, .e-control.e-control-wrapper.e-time-wrapper {
  -webkit-tap-highlight-color: transparent;
}

.e-time-wrapper .e-time-icon.e-icons, .e-control.e-control-wrapper.e-time-wrapper .e-time-icon.e-icons {
  font-size: 16px;
}

.e-time-wrapper .e-time-icon.e-icons.e-disabled, .e-control.e-control-wrapper.e-time-wrapper .e-time-icon.e-icons.e-disabled {
  pointer-events: none;
}

.e-time-wrapper span, .e-control.e-control-wrapper.e-time-wrapper span {
  cursor: pointer;
}

.e-control.e-timepicker.e-time-modal {
  background-color: rgba(255, 255, 255, 0.6);
  height: 100%;
  left: 0;
  opacity: .5;
  pointer-events: auto;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
}

.e-control.e-timepicker.e-popup {
  border-style: solid;
  border-width: 1px;
  overflow: auto;
}

.e-control.e-timepicker.e-popup .e-content {
  position: relative;
}

.e-control.e-timepicker.e-popup .e-list-parent.e-ul {
  margin: 0;
  padding: 0 0;
}

.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item {
  cursor: default;
  font-size: 14px;
  overflow: hidden;
  position: relative;
  text-overflow: ellipsis;
  vertical-align: middle;
  white-space: nowrap;
  width: 100%;
}

.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item.e-hover {
  cursor: pointer;
}

.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item {
  line-height: 36px;
  text-indent: 10px;
}

.e-bigger .e-time-wrapper .e-time-icon.e-icons, .e-control.e-bigger.e-time-wrapper .e-time-icon.e-icons,
.e-control.e-bigger .e-control-wrapper .e-time-wrapper .e-time-icon.e-icons, .e-control.e-control-wrapper.e-bigger.e-time-wrapper .e-time-icon.e-icons {
  font-size: 20px;
}

.e-bigger .e-control.e-timepicker.e-popup .e-list-parent.e-ul, .e-control.e-bigger.e-control.e-timepicker.e-popup .e-list-parent.e-ul {
  padding: 0 0;
}

.e-bigger .e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item, .e-control.e-bigger.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item {
  font-size: 14px;
  line-height: 48px;
  text-indent: 12px;
}

.e-small .e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item, .e-control.e-small.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item {
  font-size: 13px;
  line-height: 26px;
  text-indent: 10px;
}

.e-small .e-time-wrapper .e-time-icon.e-icons, .e-control.e-small.e-time-wrapper .e-time-icon.e-icons,
.e-control.e-small .e-control-wrapper.e-time-wrapper .e-time-icon.e-icons, .e-control.e-control-wrapper.e-small.e-time-wrapper .e-time-icon.e-icons {
  font-size: 14px;
}

.e-small.e-bigger .e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item, .e-control.e-small.e-bigger.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item {
  font-size: 14px;
  line-height: 40px;
  text-indent: 16px;
}

.e-small.e-bigger .e-time-wrapper .e-time-icon.e-icons, .e-control.e-small.e-bigger.e-time-wrapper .e-time-icon.e-icons,
.e-control.e-small.bigger .e-control-wrapper.e-time-wrapper .e-time-icon.e-icons, .e-control.e-control-wrapper.e-small.bigger.e-time-wrapper .e-time-icon.e-icons {
  font-size: 18px;
}

.e-content-placeholder.e-timepicker.e-placeholder-timepicker {
  background-size: 250px 33px;
  min-height: 33px;
}

.e-bigger .e-content-placeholder.e-timepicker.e-placeholder-timepicker, .e-bigger.e-content-placeholder.e-timepicker.e-placeholder-timepicker {
  background-size: 250px 40px;
  min-height: 40px;
}

/*! timepicker theme */
.e-time-wrapper .e-input-group-icon.e-icons.e-active {
  color: #000;
}

.e-time-wrapper.e-input-group:not(.e-disabled) .e-input-group-icon.e-active:active {
  color: #fff;
}

.e-control.e-timepicker.e-popup {
  border: 1px solid #757575;
  border-radius: 0;
  box-shadow: none;
}

.e-control.e-timepicker.e-popup .e-list-parent.e-ul {
  background-color: #fff;
}

.e-control.e-timepicker.e-popup .e-list-parent.e-ul li.e-list-item {
  border: 1px solid transparent;
  color: #000;
}

.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item.e-disabled {
  color: #757575;
  opacity: 1;
  pointer-events: none;
  -ms-touch-action: none;
      touch-action: none;
}

.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item.e-hover, .e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item.e-navigation, .e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item:focus {
  background-color: #ecf;
  border: 1px solid #000;
  color: #000;
}

.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item.e-active {
  background-color: #400074;
  color: #fff;
}

.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item.e-active.e-hover {
  background-color: #ecf;
  color: #000;
}

.e-small .e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item, .e-control.e-small.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item, .e-bigger.e-small .e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item, .e-control.e-bigger.e-small.e-control.e-timepicker.e-popup .e-list-parent.e-ul .e-list-item {
  color: #000;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
