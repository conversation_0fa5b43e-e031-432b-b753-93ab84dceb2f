.e-container .e-value-switch-btn::before {
  content: '\e431';
}

.e-colorpicker-wrapper {
  display: inline-block;
  line-height: 0;
  outline: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.e-colorpicker-wrapper .e-colorpicker {
  height: 1px;
  opacity: 0;
  position: absolute;
  width: 1px;
}

.e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn {
  font-family: initial;
  line-height: 17px;
  padding: 0 6px;
}

.e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn .e-selected-color {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDwhLS0gR2VuZXJhdG9yOiBTa2V0Y2ggNTAgKDU0OTgzKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA5PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ikdyb3VwLTkiPgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRTBFMEUwIiB4PSIwIiB5PSIwIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weS0yIiBmaWxsPSIjRkZGRkZGIiB4PSIwIiB5PSIzIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weSIgZmlsbD0iI0ZGRkZGRiIgeD0iMyIgeT0iMCIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExLUNvcHktMyIgZmlsbD0iI0UwRTBFMCIgeD0iMyIgeT0iMyIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
  background-size: 8px;
  border-radius: 4px;
  height: 20px;
  margin-top: 0;
  position: relative;
  width: 20px;
}

.e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn .e-selected-color .e-split-preview {
  border-radius: 4px;
}

.e-colorpicker-wrapper .e-split-btn-wrapper.e-rtl .e-split-colorpicker.e-split-btn {
  padding: 0 6px;
}

.e-colorpicker-wrapper * {
  box-sizing: border-box;
}

.e-colorpicker-wrapper.e-disabled .e-palette .e-tile {
  cursor: default;
}

.e-colorpicker-wrapper.e-disabled .e-palette .e-tile:hover {
  border: 0;
  box-shadow: none;
}

.e-colorpicker-wrapper.e-disabled .e-palette .e-tile.e-selected {
  border: 0;
}

.e-colorpicker-wrapper.e-disabled .e-container .e-handler,
.e-colorpicker-wrapper.e-disabled .e-container .e-preview-container {
  cursor: default;
}

.e-colorpicker-popup {
  line-height: 0;
}

.e-colorpicker.e-modal {
  -webkit-overflow-scrolling: touch;
  background-color: rgba(0, 0, 0, 0.6);
  height: 100%;
  left: 0;
  opacity: .5;
  pointer-events: auto;
  position: fixed;
  top: 0;
  width: 100%;
}

body.e-colorpicker-overflow {
  overflow: visible;
}

.e-bigger .e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn,
.e-bigger.e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn {
  line-height: 20px;
  padding: 0 6px;
}

.e-bigger .e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn .e-selected-color,
.e-bigger.e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn .e-selected-color {
  height: 26px;
  width: 26px;
}

.e-bigger .e-colorpicker-wrapper .e-split-btn-wrapper.e-rtl .e-split-colorpicker.e-split-btn,
.e-bigger.e-colorpicker-wrapper .e-split-btn-wrapper.e-rtl .e-split-colorpicker.e-split-btn {
  padding: 0 6px;
}

.e-container {
  border: 0;
  border-radius: 4px;
  display: inline-block;
  line-height: 0;
  outline: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  box-shadow: 0 0 15px 0 rgba(26, 26, 26, 0.8);
}

.e-container.e-color-picker {
  width: 270px;
}

.e-container.e-color-picker .e-mode-switch-btn {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjRweCIgaGVpZ2h0PSIyNHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDUwLjIgKDU1MDQ3KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA0IENvcHk8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iUGFnZS0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iQXJ0Ym9hcmQiPgogICAgICAgICAgICA8ZyBpZD0iR3JvdXAtNC1Db3B5IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgMC4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMjUtQ29weS04IiBmaWxsPSIjNDU5NkNFIiB4PSIwIiB5PSIwIiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiPjwvcmVjdD4KICAgICAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMjUtQ29weS05IiBmaWxsPSIjNUNDMTVCIiB4PSIwIiB5PSIxMiIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIj48L3JlY3Q+CiAgICAgICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTI1LUNvcHktMTAiIGZpbGw9IiNGQkQ1MDYiIHg9IjEyIiB5PSIwIiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiPjwvcmVjdD4KICAgICAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMjUtQ29weS0xMSIgZmlsbD0iI0VDNEU0MyIgeD0iMTIiIHk9IjEyIiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiPjwvcmVjdD4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+") no-repeat 100% 100%;
}

.e-container.e-color-palette {
  width: auto;
}

.e-container.e-color-palette .e-mode-switch-btn {
  background: transparent url("data:image/svg+xml;base64,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") no-repeat 100% 100%;
}

.e-container .e-custom-palette {
  display: inline-block;
}

.e-container .e-custom-palette .e-palette {
  padding: 10px;
}

.e-container .e-custom-palette.e-palette-group {
  height: 270px;
  overflow-y: scroll;
}

.e-container .e-palette {
  border-bottom: 0;
  display: table;
  line-height: 0;
  outline: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.e-container .e-palette .e-row {
  display: table-row;
  white-space: nowrap;
}

.e-container .e-palette .e-tile {
  border: 0.5px solid transparent;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  height: 27px;
  text-align: center;
  vertical-align: middle;
  width: 27px;
}

.e-container .e-palette .e-tile.e-selected {
  outline: #fff 0.5px solid;
  position: relative;
}

.e-container .e-palette .e-tile:hover {
  box-shadow: 2px 2px 7px 2px rgba(26, 26, 26, 0.3);
  position: relative;
}

.e-container .e-palette .e-tile.e-nocolor-item {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDwhLS0gR2VuZXJhdG9yOiBTa2V0Y2ggNTAgKDU0OTgzKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA5PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ikdyb3VwLTkiPgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRTBFMEUwIiB4PSIwIiB5PSIwIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weS0yIiBmaWxsPSIjRkZGRkZGIiB4PSIwIiB5PSIzIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weSIgZmlsbD0iI0ZGRkZGRiIgeD0iMyIgeT0iMCIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExLUNvcHktMyIgZmlsbD0iI0UwRTBFMCIgeD0iMyIgeT0iMyIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
  background-size: 8px;
}

.e-container .e-hsv-container {
  border-bottom: 0;
  position: relative;
  -ms-touch-action: none;
      touch-action: none;
}

.e-container .e-hsv-container .e-hsv-color {
  background: linear-gradient(to bottom, transparent 0, #000 100%), linear-gradient(to right, #fff 0, rgba(255, 255, 255, 0) 100%);
  height: 170px;
}

.e-container .e-hsv-container .e-handler {
  border: 1px solid #fff;
  border-radius: 6px;
  box-shadow: 0 0 3px rgba(26, 26, 26, 0.7), inset 0 0 2px rgba(26, 26, 26, 0.7);
  cursor: pointer;
  display: inline-block;
  height: 8px;
  margin-left: -4px;
  margin-top: -4px;
  position: absolute;
  -ms-touch-action: none;
      touch-action: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 8px;
}

.e-container .e-slider-preview {
  display: inline-block;
  width: 100%;
  padding: 9px 15px;
}

.e-container .e-slider-preview .e-colorpicker-slider {
  display: inline-block;
  width: 82.5%;
}

.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container {
  height: 18px;
}

.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-slider {
  height: 18px;
  top: calc(50% - 9px);
}

.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-slider-track {
  border-radius: 0;
  height: 6px;
  top: calc(50% - 3px);
}

.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-handle {
  border-radius: 6px;
  cursor: pointer;
  height: 14px;
  top: calc(50% - 7px);
  width: 14px;
}

.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-handle.e-handle-active {
  cursor: pointer;
}

.e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-handle.e-large-thumb-size {
  transform: scale(1);
}

.e-container .e-slider-preview .e-colorpicker-slider .e-hue-slider .e-slider-track {
  background: linear-gradient(to right, #f00 0, #ff0 16%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 84%, #ff0004 100%);
}

.e-container .e-slider-preview .e-colorpicker-slider .e-opacity-slider .e-slider-track {
  border: 0;
  z-index: 1;
}

.e-container .e-slider-preview .e-colorpicker-slider .e-opacity-slider .e-opacity-empty-track {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDwhLS0gR2VuZXJhdG9yOiBTa2V0Y2ggNTAgKDU0OTgzKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA5PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ikdyb3VwLTkiPgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRTBFMEUwIiB4PSIwIiB5PSIwIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weS0yIiBmaWxsPSIjRkZGRkZGIiB4PSIwIiB5PSIzIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weSIgZmlsbD0iI0ZGRkZGRiIgeD0iMyIgeT0iMCIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExLUNvcHktMyIgZmlsbD0iI0UwRTBFMCIgeD0iMyIgeT0iMyIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
  background-size: contain;
  border: 0;
  height: 6px;
  position: absolute;
  top: calc(50% - 3px);
  width: 100%;
  z-index: 0;
}

.e-container .e-slider-preview .e-colorpicker-slider .e-slider.e-hue-slider .e-handle,
.e-container .e-slider-preview .e-colorpicker-slider .e-slider.e-hue-slider .e-handle-start,
.e-container .e-slider-preview .e-colorpicker-slider .e-slider.e-opacity-slider .e-handle,
.e-container .e-slider-preview .e-colorpicker-slider .e-slider.e-opacity-slider .e-handle-start {
  box-shadow: 0 1px 2px rgba(26, 26, 26, 0.64);
}

.e-container .e-slider-preview .e-preview-container {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDwhLS0gR2VuZXJhdG9yOiBTa2V0Y2ggNTAgKDU0OTgzKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA5PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ikdyb3VwLTkiPgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRTBFMEUwIiB4PSIwIiB5PSIwIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weS0yIiBmaWxsPSIjRkZGRkZGIiB4PSIwIiB5PSIzIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weSIgZmlsbD0iI0ZGRkZGRiIgeD0iMyIgeT0iMCIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExLUNvcHktMyIgZmlsbD0iI0UwRTBFMCIgeD0iMyIgeT0iMyIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
  background-size: 10px;
  border: 1px solid #585858;
  cursor: pointer;
  display: inline-block;
  height: 32px;
  margin-left: 4.17%;
  position: relative;
  top: -2px;
  width: 13.338%;
}

.e-container .e-slider-preview .e-preview-container .e-preview {
  display: block;
  height: 16px;
  position: absolute;
  width: 100%;
}

.e-container .e-slider-preview .e-preview-container .e-preview.e-current {
  border-bottom: 1px solid #585858;
  top: 0;
}

.e-container .e-slider-preview .e-preview-container .e-preview.e-previous {
  height: 16px;
  top: 16px;
}

.e-container .e-selected-value {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0 15px 15px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
}

.e-container .e-selected-value .e-input-container .e-float-input input {
  padding-left: 0;
  text-align: center;
}

.e-container .e-selected-value .e-value-switch-btn {
  font-family: 'e-icons';
  font-size: 16px;
  line-height: 16px;
  margin-top: 22px;
  padding: 2px 3px;
}

.e-container .e-input-container {
  display: inline-block;
  width: 90%;
}

.e-container .e-input-container .e-float-input {
  display: inline-block;
  margin-right: 1.856%;
  vertical-align: baseline;
}

.e-container .e-input-container .e-float-input:first-child {
  width: 30%;
}

.e-container .e-input-container .e-float-input:first-child input {
  height: 32px;
}

.e-container .e-input-container .e-float-input.e-numeric {
  height: 32px;
  width: 15%;
}

.e-container .e-input-container .e-float-input.e-numeric input {
  height: 30px;
}

.e-container .e-input-container .e-float-input .e-float-text {
  text-align: center;
}

.e-container .e-switch-ctrl-btn {
  display: inline-block;
  padding: 0 15px 15px;
  white-space: nowrap;
  width: 100%;
}

.e-container .e-switch-ctrl-btn .e-ctrl-btn {
  float: right;
  text-align: right;
  white-space: nowrap;
  width: 89.913%;
}

.e-container .e-switch-ctrl-btn .e-ctrl-btn .e-btn {
  max-width: 47.29%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.e-container .e-switch-ctrl-btn .e-ctrl-btn .e-btn.e-cancel {
  margin-left: 3.71%;
}

.e-container .e-switch-ctrl-btn .e-mode-switch-btn {
  background-origin: border-box;
  background-position: center;
  background-size: cover;
  float: left;
  margin-top: 2px;
  overflow: hidden;
  white-space: nowrap;
}

.e-container .e-switch-ctrl-btn .e-mode-switch-btn:focus {
  box-shadow: none;
}

.e-container .e-value-switch-btn,
.e-container .e-mode-switch-btn {
  background-color: transparent;
  border-color: transparent;
  position: relative;
  width: 10%;
  height: 24px;
}

.e-container.e-color-picker .e-value-switch-btn:focus, .e-container.e-color-picker .e-value-switch-btn:hover, .e-container.e-color-picker .e-value-switch-btn:active {
  border-color: transparent;
  box-shadow: none;
  outline: none;
}

.e-container.e-color-picker .e-value-switch-btn:focus {
  outline: none;
}

.e-container.e-color-palette .e-palette + .e-selected-value,
.e-container.e-color-palette .e-palette-group + .e-selected-value {
  padding: 15px 15px;
}

.e-container.e-color-palette .e-palette + .e-switch-ctrl-btn,
.e-container.e-color-palette .e-palette-group + .e-switch-ctrl-btn {
  padding: 25px 15px 15px;
}

.e-hide-opacity .e-container .e-slider-preview .e-colorpicker-slider {
  vertical-align: super;
}

.e-hide-opacity .e-container .e-slider-preview .e-preview-container {
  top: 0;
}

.e-hide-opacity .e-container .e-float-input:first-child {
  width: 36%;
}

.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 16%;
}

.e-hide-hex-value .e-container .e-float-input.e-numeric {
  width: 21%;
}

.e-hide-hex-value.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 29.59%;
}

.e-hide-valueswitcher .e-container .e-input-container {
  width: 100%;
}

.e-hide-valueswitcher .e-container .e-float-input:first-child {
  width: 30%;
}

.e-hide-valueswitcher .e-container .e-float-input.e-numeric {
  width: 13%;
}

.e-hide-valueswitcher .e-container .e-float-input:last-child {
  margin-right: 0;
}

.e-hide-valueswitcher.e-hide-opacity .e-container .e-float-input:first-child {
  width: 36%;
}

.e-hide-valueswitcher.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 17.58%;
}

.e-hide-valueswitcher.e-hide-hex-value .e-container .e-float-input.e-numeric {
  width: 22%;
}

.e-hide-valueswitcher.e-hide-hex-value.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 30.82%;
}

.e-rtl .e-container .e-hsv-container .e-hsv-color {
  background: linear-gradient(to bottom, transparent 0, #000 100%), linear-gradient(to left, #fff 0, rgba(255, 255, 255, 0) 100%);
}

.e-rtl .e-container .e-slider-preview .e-hue-slider .e-slider-track {
  background: linear-gradient(to left, #f00 0, #ff0 16%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 84%, #ff0004 100%);
}

.e-rtl .e-container .e-slider-preview .e-preview-container {
  margin-left: 0;
  margin-right: 4.17%;
}

.e-rtl .e-container .e-selected-value .e-float-input {
  margin-left: 1.856%;
  margin-right: 0;
}

.e-rtl .e-container .e-selected-value .e-float-input input {
  padding-right: 0;
}

.e-rtl .e-container .e-selected-value .e-value-switch-btn {
  transform: rotate(180deg);
}

.e-rtl .e-container .e-switch-ctrl-btn .e-mode-switch-btn {
  float: right;
}

.e-rtl .e-container .e-switch-ctrl-btn .e-ctrl-btn {
  float: left;
  text-align: left;
}

.e-rtl .e-container .e-switch-ctrl-btn .e-ctrl-btn .e-cancel {
  margin-left: 0;
  margin-right: 3.71%;
}

.e-rtl.e-hide-valueswitcher .e-container .e-float-input:last-child {
  margin-left: 0;
}

.e-bigger .e-container.e-color-picker {
  width: 290px;
}

.e-bigger .e-container .e-palette .e-tile {
  height: 29px;
  width: 29px;
}

.e-bigger .e-container .e-hsv-container .e-hsv-color {
  height: 180px;
}

.e-bigger .e-container .e-custom-palette.e-palette-group {
  height: 290px;
}

.e-bigger .e-container .e-slider-preview {
  padding: 7px 15px;
}

.e-bigger .e-container .e-slider-preview .e-preview-container {
  margin-left: 3.85%;
  top: 0;
  vertical-align: super;
  width: 12.31%;
}

.e-bigger .e-container .e-slider-preview .e-colorpicker-slider {
  width: 83.7%;
}

.e-bigger .e-container .e-slider-preview .e-colorpicker-slider .e-slider-container {
  height: 22px;
}

.e-bigger .e-container .e-slider-preview .e-colorpicker-slider .e-slider-container .e-handle {
  border-radius: 8px;
  height: 16px;
  top: calc(50% - 8px);
  width: 16px;
}

.e-bigger .e-container .e-slider-preview .e-switch-ctrl-btn .e-ctrl-btn {
  width: 90.6%;
}

.e-bigger .e-container.e-color-palette .e-palette + .e-switch-ctrl-btn {
  padding: 25px 15px 15px;
}

.e-bigger .e-container .e-input-container {
  width: 90.77%;
}

.e-bigger .e-container .e-input-container .e-float-input {
  margin-right: 1.699%;
}

.e-bigger .e-container .e-input-container .e-float-input:first-child {
  width: 34.9%;
}

.e-bigger .e-container .e-input-container .e-float-input:first-child input {
  height: 38px;
}

.e-bigger .e-container .e-input-container .e-float-input.e-numeric {
  height: 38px;
  width: 12.3%;
}

.e-bigger .e-container .e-input-container .e-float-input.e-numeric input {
  height: 36px;
}

.e-bigger .e-container .e-css.e-value-switch-btn,
.e-bigger .e-container .e-mode-switch-btn {
  font-size: 18px;
  line-height: 18px;
  padding: 0 2px;
  width: 9.235%;
}

.e-bigger .e-container .e-mode-switch-btn {
  margin-top: 6px;
}

.e-bigger .e-hide-opacity .e-container .e-slider-preview, .e-bigger.e-hide-opacity .e-container .e-slider-preview {
  padding: 7px 15px;
}

.e-bigger .e-hide-opacity .e-container .e-slider-preview .e-preview-container, .e-bigger.e-hide-opacity .e-container .e-slider-preview .e-preview-container {
  vertical-align: initial;
}

.e-bigger .e-hide-opacity .e-container .e-float-input:first-child, .e-bigger.e-hide-opacity .e-container .e-float-input:first-child {
  width: 36%;
}

.e-bigger .e-hide-opacity .e-container .e-float-input.e-numeric, .e-bigger.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 16%;
}

.e-bigger.e-hide-hex-value .e-container .e-float-input.e-numeric,
.e-bigger .e-hide-hex-value .e-container .e-float-input.e-numeric {
  width: 21.58%;
}

.e-bigger.e-hide-hex-value.e-hide-opacity .e-container .e-float-input.e-numeric,
.e-bigger .e-hide-hex-value.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 29.59%;
}

.e-bigger.e-hide-valueswitcher .e-container .e-input-container,
.e-bigger .e-hide-valueswitcher .e-container .e-input-container {
  width: 100%;
}

.e-bigger.e-hide-valueswitcher .e-container .e-float-input:last-child,
.e-bigger .e-hide-valueswitcher .e-container .e-float-input:last-child {
  margin-right: 0;
}

.e-bigger.e-hide-valueswitcher .e-container .e-float-input.e-numeric,
.e-bigger .e-hide-valueswitcher .e-container .e-float-input.e-numeric {
  width: 13.2%;
}

.e-bigger.e-hide-valueswitcher.e-hide-opacity .e-container .e-float-input:first-child,
.e-bigger .e-hide-valueswitcher.e-hide-opacity .e-container .e-float-input:first-child {
  width: 36%;
}

.e-bigger.e-hide-valueswitcher.e-hide-opacity .e-container .e-float-input.e-numeric,
.e-bigger .e-hide-valueswitcher.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 17.9%;
}

.e-bigger.e-hide-valueswitcher.e-hide-hex-value .e-container .e-float-input.e-numeric,
.e-bigger.e-hide-valueswitcher .e-hide-hex-value .e-container .e-float-input.e-numeric,
.e-bigger .e-hide-valueswitcher.e-hide-hex-value .e-container .e-float-input.e-numeric,
.e-bigger .e-hide-valueswitcher .e-hide-hex-value .e-container .e-float-input.e-numeric {
  width: 21.58%;
}

.e-bigger.e-hide-valueswitcher.e-hide-hex-value.e-hide-opacity .e-container .e-float-input.e-numeric,
.e-bigger.e-hide-valueswitcher .e-hide-hex-value.e-hide-opacity .e-container .e-float-input.e-numeric,
.e-bigger .e-hide-valueswitcher.e-hide-hex-value.e-hide-opacity .e-container .e-float-input.e-numeric,
.e-bigger .e-hide-valueswitcher .e-hide-hex-value.e-hide-opacity .e-container .e-float-input.e-numeric {
  width: 30.82%;
}

.e-bigger .e-rtl .e-container .e-slider-preview .e-preview-container,
.e-bigger.e-rtl .e-container .e-slider-preview .e-preview-container {
  margin-left: 0;
  margin-right: 3.85%;
}

.e-bigger .e-rtl .e-container .e-selected-value .e-float-input,
.e-bigger.e-rtl .e-container .e-selected-value .e-float-input {
  margin-left: 1.699%;
  margin-right: 0;
}

.e-bigger .e-rtl.e-hide-valueswitcher .e-container .e-float-input:last-child,
.e-bigger.e-rtl.e-hide-valueswitcher .e-container .e-float-input:last-child {
  margin-left: 0;
}

.e-hide-switchable-value .e-container .e-input-container .e-float-input:first-child,
.e-bigger.e-hide-switchable-value .e-container .e-input-container .e-float-input:first-child,
.e-bigger .e-hide-switchable-value .e-container .e-input-container .e-float-input:first-child {
  width: 100%;
}

.e-popup.e-tooltip-wrap.e-color-picker-tooltip {
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 0%;
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  box-shadow: 0 1px 2px rgba(26, 26, 26, 0.64);
  cursor: pointer;
  min-width: 26px;
  transform: translateY(18px) rotate(45deg) scale(0.01);
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.e-popup.e-tooltip-wrap.e-color-picker-tooltip .e-tip-content {
  background: transparent url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNnB4IiBoZWlnaHQ9IjZweCIgdmlld0JveD0iMCAwIDYgNiIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDwhLS0gR2VuZXJhdG9yOiBTa2V0Y2ggNTAgKDU0OTgzKSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4KICAgIDx0aXRsZT5Hcm91cCA5PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ikdyb3VwLTkiPgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExIiBmaWxsPSIjRTBFMEUwIiB4PSIwIiB5PSIwIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weS0yIiBmaWxsPSIjRkZGRkZGIiB4PSIwIiB5PSIzIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSJSZWN0YW5nbGUtMTEtQ29weSIgZmlsbD0iI0ZGRkZGRiIgeD0iMyIgeT0iMCIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlLTExLUNvcHktMyIgZmlsbD0iI0UwRTBFMCIgeD0iMyIgeT0iMyIgd2lkdGg9IjMiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
  background-size: 8px;
  border-radius: 50%;
  height: 24px;
  position: relative;
  transform: rotate(45deg);
  width: 24px;
}

.e-split-preview,
.e-tip-transparent {
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.e-bigger .e-popup.e-tooltip-wrap.e-color-picker-tooltip,
.e-bigger.e-popup.e-tooltip-wrap.e-color-picker-tooltip {
  min-width: 32px;
}

.e-bigger .e-popup.e-tooltip-wrap.e-color-picker-tooltip .e-tip-content,
.e-bigger.e-popup.e-tooltip-wrap.e-color-picker-tooltip .e-tip-content {
  height: 30px;
  width: 30px;
}

.e-container {
  background-color: #2a2a2a;
}

.e-container .e-palette .e-tile:hover {
  border-color: #000;
}

.e-container .e-palette .e-tile.e-selected {
  border-color: #000;
}

.e-container .e-handler.e-hide-handler,
.e-container .e-handle.e-hide-handler {
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
}

.e-container .e-control-wrapper.e-slider-container .e-slider.e-hue-slider .e-handle:not(.e-tab-handle),
.e-container .e-control-wrapper.e-slider-container .e-slider.e-opacity-slider .e-handle:not(.e-tab-handle) {
  background-color: #fff;
  border-color: #fff;
}

.e-container .e-control-wrapper.e-slider-container .e-slider.e-hue-slider .e-handle:not(.e-tab-handle).e-handle-start:not(.e-tab-handle),
.e-container .e-control-wrapper.e-slider-container .e-slider.e-opacity-slider .e-handle:not(.e-tab-handle).e-handle-start:not(.e-tab-handle) {
  background-color: #fff;
  border-color: #fff;
}

.e-container .e-control-wrapper.e-slider-container .e-slider.e-hue-slider .e-handle.e-tab-handle,
.e-container .e-control-wrapper.e-slider-container .e-slider.e-opacity-slider .e-handle.e-tab-handle {
  background-color: #fff;
  border-color: #fff;
  box-shadow: 0 1px 2px rgba(26, 26, 26, 0.64);
}

.e-colorpicker-wrapper.e-disabled .e-value-switch-btn,
.e-colorpicker-wrapper.e-disabled .e-mode-switch-btn {
  color: rgba(240, 240, 240, 0.3);
}

.e-colorpicker-wrapper.e-disabled .e-value-switch-btn:focus,
.e-colorpicker-wrapper.e-disabled .e-mode-switch-btn:focus {
  background-color: transparent;
  color: rgba(240, 240, 240, 0.3);
  outline: none;
  outline-offset: none;
}

.e-colorpicker-wrapper.e-disabled .e-value-switch-btn:active,
.e-colorpicker-wrapper.e-disabled .e-mode-switch-btn:active {
  background-color: transparent;
  color: rgba(240, 240, 240, 0.3);
}

.e-popup.e-tooltip-wrap.e-color-picker-tooltip {
  background-color: #fff;
  border-color: #fff;
}
