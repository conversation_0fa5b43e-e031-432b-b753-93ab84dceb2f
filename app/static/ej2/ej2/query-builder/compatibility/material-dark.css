@charset "UTF-8";
@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
.e-ddl.e-control-wrapper .e-ddl-icon::before {
  transform: rotate(0deg);
  transition: transform 300ms ease;
}

.e-ddl.e-control-wrapper.e-icon-anim .e-ddl-icon::before {
  transform: rotate(180deg);
  transition: transform 300ms ease;
}

.e-dropdownbase .e-list-item.e-active.e-hover {
  color: #00b0ff;
}

.e-input-group:not(.e-disabled) .e-control.e-control.e-dropdownlist ~ .e-ddl-icon:active, .e-input-group:not(.e-disabled) .e-control.e-control.e-dropdownlist ~ .e-ddl-icon:hover, .e-input-group:not(.e-disabled) .e-back-icon:active, .e-input-group:not(.e-disabled) .e-back-icon:hover, .e-control.e-popup.e-ddl .e-input-group:not(.e-disabled) .e-clear-icon:active, .e-control.e-popup.e-ddl .e-input-group:not(.e-disabled) .e-clear-icon:hover {
  background: transparent;
}

.e-input-group .e-ddl-icon:not(:active)::after {
  animation: none;
}

.e-ddl.e-control.e-popup {
  border: 0;
  box-shadow: 0 2px 3px 1px rgba(0, 0, 0, 0.21);
  margin-top: 2px;
}

.e-bigger .e-control.e-popup.e-ddl-device-filter .e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) {
  border-color: transparent;
}

.e-bigger .e-control.e-popup.e-ddl-device-filter {
  margin-top: 0;
}

.e-bigger .e-ddl-device .e-input-group, .e-bigger .e-ddl-device .e-input-group.e-input-focus:not(.e-success):not(.e-warning):not(.e-error) {
  background: #f5f5f5;
  border-width: 0;
  box-shadow: 0;
  margin-bottom: 0;
}

.e-bigger .e-ddl-device .e-input-group .e-back-icon, .e-bigger .e-ddl-device .e-input-group input.e-input, .e-bigger .e-ddl-device .e-input-group .e-clear-icon {
  background-color: #f5f5f5;
}

.e-control.e-popup.e-ddl:not(.e-ddl-device) .e-input-group .e-clear-icon {
  margin: 6px 6px 5px;
  min-height: 12px;
  min-width: 12px;
  padding: 6px;
}

.e-bigger .e-control.e-popup.e-ddl:not(.e-ddl-device) .e-input-group .e-clear-icon {
  min-height: 16px;
  min-width: 16px;
}

.e-bigger .e-control.e-popup.e-ddl:not(.e-ddl-device) .e-filter-parent .e-input-filter {
  padding: 8px 16px 8px 0;
}

.e-input-group.e-ddl, .e-input-group.e-ddl .e-input, .e-input-group.e-ddl .e-ddl-icon {
  background: transparent;
}

.e-ddl.e-ddl-device.e-ddl-device-filter .e-input-group:hover:not(.e-disabled):not(.e-float-icon-left), .e-ddl.e-ddl-device.e-ddl-device-filter .e-input-group.e-control-wrapper:hover:not(.e-disabled):not(.e-float-icon-left) {
  border-bottom-width: 0;
}

.e-control.e-popup.e-ddl:not(.e-ddl-device) .e-input-group.e-small .e-clear-icon, .e-small .e-control.e-popup.e-ddl:not(.e-ddl-device) .e-input-group .e-clear-icon, .e-control.e-popup.e-ddl:not(.e-ddl-device) .e-input-group.e-input-focus.e-small .e-clear-icon, .e-small .e-control.e-popup.e-ddl:not(.e-ddl-device) .e-input-group.e-input-focus .e-clear-icon {
  margin: 0;
}

.e-small .e-control.e-popup.e-ddl:not(.e-ddl-device) .e-filter-parent .e-input-group .e-input-filter, .e-control.e-popup.e-ddl:not(.e-ddl-device) .e-filter-parent .e-input-group.e-small .e-input-filter, .e-small .e-control.e-popup.e-ddl:not(.e-ddl-device) .e-filter-parent .e-input-group.e-input-focus .e-input-filter, .e-control.e-popup.e-ddl:not(.e-ddl-device) .e-filter-parent .e-input-group.e-small.e-input-focus .e-input-filter {
  padding: 5px 5px 5px 12px;
}

@keyframes material-spinner-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fabric-spinner-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.e-query-builder .e-collapse-rule::before {
  content: '\e907';
}

.e-query-builder {
  border: 1px solid;
  border-radius: 0;
  height: auto;
  width: auto;
}

.e-query-builder .e-multiselect .e-qb-spinner .e-spinner-inner {
  left: auto;
  right: 5px;
}

.e-query-builder.e-rtl .e-multiselect .e-qb-spinner .e-spinner-inner {
  left: 5px;
  right: auto;
}

.e-query-builder.e-rtl.e-bigger.e-device .e-group-body .e-rule-container .e-rule-delete, .e-query-builder.e-rtl.e-bigger .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete {
  padding-left: 0;
}

.e-query-builder.e-rtl.e-device .e-group-body .e-rule-container .e-rule-value-delete, .e-query-builder.e-rtl .e-group-body .e-rule-container.e-vertical-mode .e-rule-value-delete {
  text-align: left;
}

.e-query-builder.e-rtl.e-device .e-group-body .e-rule-container .e-rule-delete, .e-query-builder.e-rtl .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete {
  padding-left: 0;
}

.e-query-builder.e-rtl .e-group-header .e-group-action .e-btn {
  margin-left: 0;
  margin-right: 12px;
}

.e-query-builder.e-rtl .e-horizontal-mode .e-rule-delete {
  margin-left: 0;
  margin-right: 12px;
}

.e-query-builder.e-rtl .e-group-body {
  padding-left: 0;
  padding-right: 20px;
}

.e-query-builder.e-rtl .e-rule-list > ::before {
  border-width: 0 2px 2px 0;
}

.e-query-builder.e-rtl .e-rule-list > .e-group-container:first-child {
  margin-top: 0;
}

.e-query-builder.e-rtl .e-rule-list > ::after, .e-query-builder.e-rtl .e-rule-list > ::before {
  right: -12px;
}

.e-query-builder.e-rtl .e-rule-list > ::after {
  border-width: 0 2px 0 0;
}

.e-query-builder.e-rtl .e-rule-list .e-group-container::before {
  right: none;
}

.e-query-builder.e-rtl .e-rule-list > .e-group-container {
  padding-right: 0;
}

.e-query-builder .e-group-container, .e-query-builder .e-rule-container {
  position: relative;
}

.e-query-builder .e-rule-list > :first-child::before {
  top: -15px;
}

.e-query-builder .e-rule-list > :last-child::after {
  display: none;
}

.e-query-builder .e-rule-list > ::before {
  border-width: 0 0 2px 2px;
  height: 25px;
  top: -10px;
}

.e-query-builder .e-rule-list > ::after, .e-query-builder .e-rule-list > ::before {
  border-style: dotted;
  content: '';
  left: -12px;
  position: absolute;
  width: 10px;
}

.e-query-builder .e-rule-list > ::after {
  border-width: 0 0 0 2px;
  height: calc(100% - 17px);
  top: 17px;
}

.e-query-builder .e-rule-list > .e-rule-container::before {
  height: calc(50% + 8px);
}

.e-query-builder .e-rule-list > .e-rule-container:not(:first-child)::before {
  height: calc(50% + 12px);
}

.e-query-builder .e-rule-list > .e-rule-container::after {
  height: calc(50% + 6px);
  top: calc(50% - 3px);
}

.e-query-builder .e-rule-list > .e-group-container:first-child {
  margin-top: 0;
}

.e-query-builder .e-rule-list .e-group-container::before {
  left: none;
}

.e-query-builder .e-rule-list .e-group-container::after {
  left: -12px;
}

.e-query-builder .e-group-header .e-group-action .e-btn {
  margin-left: 12px;
}

.e-query-builder .e-group-header .e-btn-group {
  border: none;
  box-shadow: none;
  display: inline-block;
}

.e-query-builder .e-group-header .e-group-action {
  display: inline-block;
  margin-top: 0;
}

.e-query-builder .e-group-header .e-dropdown-btn.e-add-btn, .e-query-builder .e-group-header .e-deletegroup {
  margin-bottom: 2px;
}

.e-query-builder .e-group-header button.e-button-hide {
  display: none;
}

.e-query-builder .e-group-header.e-btn.e-small.e-round {
  box-shadow: none;
}

.e-query-builder .e-rule-list .e-group-container {
  margin-left: 0;
  margin-top: 12px;
  width: 100%;
}

.e-query-builder .e-rule-list {
  padding: 12px 0 12px 0;
  padding-bottom: 0;
}

.e-query-builder .e-group-container {
  padding: 12px;
}

.e-query-builder .e-rule-container {
  border: none;
  border-radius: 0;
}

.e-query-builder .e-rule-list > .e-group-container {
  padding: 0;
  padding-bottom: 12px;
}

.e-query-builder .e-group-container .e-rule-list > .e-group-container {
  padding-bottom: 0;
}

.e-query-builder .e-group-body {
  padding-left: 20px;
}

.e-query-builder .e-group-body .e-rule-container {
  box-shadow: 0 2px 6px #424242;
  height: auto;
  margin-top: 0;
  padding-right: 12px;
}

.e-query-builder .e-group-body .e-rule-container.e-prev-joined-rule {
  border-bottom: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.e-query-builder .e-group-body .e-rule-container.e-joined-rule {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-top-style: dotted;
  border-top-width: 2px;
  margin-top: 0;
}

.e-query-builder .e-group-body .e-rule-container.e-separate-rule {
  margin-top: 12px;
}

.e-query-builder .e-group-body .e-rule-container .e-rule-filter {
  padding: 12px 0 12px 12px;
  width: auto;
}

.e-query-builder .e-group-body .e-rule-container .e-rule-value .e-control-wrapper.e-numeric:not(:first-child), .e-query-builder .e-group-body .e-rule-container .e-rule-value .e-control-wrapper.e-date-wrapper:not(:first-child), .e-query-builder .e-group-body .e-rule-container .e-rule-value .e-control-wrapper.e-input-group:not(:first-child) {
  float: right;
  margin-top: 12px;
}

.e-query-builder .e-group-body .e-rule-container .e-rule-value .e-multi-select-wrapper {
  min-width: 190px;
}

.e-query-builder .e-group-body .e-rule-container .e-rule-value .e-multiselect, .e-query-builder .e-group-body .e-rule-container .e-rule-value .e-multi-select-wrapper {
  max-width: 200p;
}

.e-query-builder .e-group-body .e-rule-container .e-operator, .e-query-builder .e-group-body .e-rule-container .e-value {
  padding: 12px 0 12px 12px;
}

.e-query-builder .e-group-body .e-rule-container .e-operator .e-radio-wrapper, .e-query-builder .e-group-body .e-rule-container .e-value .e-radio-wrapper {
  margin-right: 15px;
}

.e-query-builder .e-group-body .e-horizontal-mode .e-rule-delete {
  display: inline-block;
  margin-left: 12px;
}

.e-query-builder .e-group-body .e-rule-container button.e-button-hide {
  display: none;
}

.e-query-builder .e-group-body .e-horizontal-mode .e-rule-filter, .e-query-builder .e-group-body .e-horizontal-mode .e-rule-operator, .e-query-builder .e-group-body .e-horizontal-mode .e-rule-value, .e-query-builder .e-group-body .e-horizontal-mode .e-rule-value-delete {
  display: inline-block;
}

.e-query-builder .e-group-body .e-rule-value.e-hide {
  display: none;
}

.e-query-builder .e-group-body .e-rule-value.e-show {
  display: inline-block;
}

.e-query-builder .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete {
  margin-bottom: 12px;
  padding-right: 0;
  right: 0;
}

.e-query-builder .e-group-body .e-rule-field.e-btn.e-small.e-round, .e-query-builder .e-group-body .e-rule-delete.e-btn.e-small.e-round {
  box-shadow: none;
}

.e-query-builder .e-group-body .e-vertical-mode .e-removerule.e-rule-delete {
  box-shadow: none;
  right: 0;
}

.e-query-builder .e-dropdown-btn {
  box-shadow: 0 2px 6px #424242;
}

.e-query-builder.e-device .e-group-body .e-rule-container .e-rule-filter {
  padding: 16px;
}

.e-query-builder.e-device .e-removerule.e-rule-delete {
  box-shadow: none;
}

.e-query-builder.e-device .e-group-body .e-rule-container .e-rule-value .e-multiselect, .e-query-builder.e-device .e-group-body .e-rule-container .e-rule-value .e-multi-select-wrapper {
  max-width: 100;
}

.e-query-builder.e-device .e-group-body .e-rule-container .e-rule-value-delete, .e-query-builder .e-group-body .e-rule-container.e-vertical-mode .e-rule-value-delete {
  text-align: right;
}

.e-query-builder .e-delete-icon::before {
  content: "";
}

.e-query-builder .e-edit-rule.e-btn.e-small {
  box-shadow: none;
}

.e-query-builder .e-edit-rule {
  right: 0;
}

.e-query-builder .e-collapse-rule {
  border: 1px solid;
  border-right: 0;
  border-top: 0;
  box-shadow: -1px 1px 4px 0 rgba(0, 0, 0, 0.12);
  font-size: 20px;
  padding: 5px;
  position: absolute;
  right: 0;
  top: 0;
}

.e-query-builder .e-summary-text {
  border-style: none;
  font-family: "Roboto", "Segoe UI", "GeezaPro", "DejaVu Serif", "sans-serif", "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  line-height: 1.5em;
  padding: 12px;
  resize: none;
  width: 100%;
}

.e-query-builder .e-summary-btndiv {
  padding: 8px 0 5px 8px;
  text-align: right;
}

.e-query-builder .e-rule-list .e-rule-container.e-vertical-mode .e-rule-filter, .e-query-builder .e-rule-list .e-rule-container.e-vertical-mode .e-operator, .e-query-builder .e-rule-list .e-rule-container.e-vertical-mode .e-value, .e-query-builder .e-rule-list .e-rule-container.e-vertical-mode .e-rule-value-delete, .e-query-builder.e-device .e-rule-list .e-rule-container .e-rule-filter, .e-query-builder.e-device .e-rule-list .e-rule-container .e-operator, .e-query-builder.e-device .e-rule-list .e-rule-container .e-value, .e-query-builder.e-device .e-rule-list .e-rule-container .e-rule-value-delete {
  padding: 16px 0 0 16px;
}

.e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-inner.e-tip-top, .e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-outer.e-tip-top {
  border-bottom: 8px solid;
}

.e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-outer.e-tip-bottom, .e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-inner.e-tip-bottom {
  border-top: 8px solid;
}

.e-dropdown-popup .e-addgroup {
  float: right;
}

.e-dropdown-popup ul .e-item.e-button-hide {
  display: none;
}

.e-addrule.e-add-icon::before, .e-addgroup.e-add-icon::before, .e-query-builder .e-add-icon::before {
  content: "";
}

.e-query-builder.e-device .e-group-container, .e-bigger.e-query-builder .e-group-container, .e-bigger .e-query-builder .e-group-container {
  padding: 16px;
}

.e-query-builder.e-device .e-rule-list > .e-group-container, .e-bigger.e-query-builder .e-rule-list > .e-group-container, .e-bigger .e-query-builder .e-rule-list > .e-group-container {
  padding: 0;
}

.e-query-builder.e-device .e-rule-list > .e-rule-container:not(:first-child)::before, .e-bigger.e-query-builder .e-rule-list > .e-rule-container:not(:first-child)::before, .e-bigger .e-query-builder .e-rule-list > .e-rule-container:not(:first-child)::before {
  height: calc(50% + 12px);
}

.e-query-builder.e-device .e-rule-list > .e-group-container:first-child, .e-bigger.e-query-builder .e-rule-list > .e-group-container:first-child, .e-bigger .e-query-builder .e-rule-list > .e-group-container:first-child {
  margin-top: 0;
}

.e-query-builder.e-device .e-rule-list > ::before, .e-bigger.e-query-builder .e-rule-list > ::before, .e-bigger .e-query-builder .e-rule-list > ::before {
  top: -12px;
}

.e-query-builder.e-device .e-group-action .e-btn, .e-bigger.e-query-builder .e-group-action .e-btn, .e-bigger .e-query-builder .e-group-action .e-btn {
  margin-left: 16px;
}

.e-query-builder.e-device .e-rule-list > :first-child::before, .e-bigger.e-query-builder .e-rule-list > :first-child::before, .e-bigger .e-query-builder .e-rule-list > :first-child::before {
  top: -16px;
}

.e-query-builder.e-device .e-rule-list, .e-bigger.e-query-builder .e-rule-list, .e-bigger .e-query-builder .e-rule-list {
  padding: 15px 0 15px 0;
  padding-bottom: 0;
}

.e-query-builder.e-device .e-rule-list .e-group-container, .e-bigger.e-query-builder .e-rule-list .e-group-container, .e-bigger .e-query-builder .e-rule-list .e-group-container {
  margin-top: 16px;
}

.e-query-builder.e-device .e-group-body, .e-bigger.e-query-builder .e-group-body, .e-bigger .e-query-builder .e-group-body {
  padding-left: 24px;
}

.e-query-builder.e-device .e-group-body .e-rule-container, .e-bigger.e-query-builder .e-group-body .e-rule-container, .e-bigger .e-query-builder .e-group-body .e-rule-container {
  margin-top: 0;
  padding-right: 16px;
}

.e-query-builder.e-device .e-group-body .e-rule-container.e-vertical-mode, .e-bigger.e-query-builder .e-group-body .e-rule-container.e-vertical-mode, .e-bigger .e-query-builder .e-group-body .e-rule-container.e-vertical-mode {
  width: auto;
}

.e-query-builder.e-device .e-group-body .e-rule-container.e-separate-rule, .e-bigger.e-query-builder .e-group-body .e-rule-container.e-separate-rule, .e-bigger .e-query-builder .e-group-body .e-rule-container.e-separate-rule {
  margin-top: 16px;
}

.e-query-builder.e-device .e-group-body .e-rule-delete, .e-query-builder.e-device .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete, .e-bigger.e-query-builder .e-group-body .e-rule-delete, .e-bigger.e-query-builder .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete, .e-bigger .e-query-builder .e-group-body .e-rule-delete, .e-bigger .e-query-builder .e-group-body .e-rule-container.e-vertical-mode .e-rule-delete {
  margin-bottom: 16px;
  padding-right: 0;
  right: 0;
}

.e-query-builder.e-device .e-group-body .e-rule-container.e-horizontal-mode .e-rule-delete, .e-bigger.e-query-builder .e-group-body .e-rule-container.e-horizontal-mode .e-rule-delete, .e-bigger .e-query-builder .e-group-body .e-rule-container.e-horizontal-mode .e-rule-delete {
  margin-bottom: 0;
}

.e-query-builder.e-device .e-group-body .e-horizontal-mode .e-rule-delete, .e-bigger.e-query-builder .e-group-body .e-horizontal-mode .e-rule-delete, .e-bigger .e-query-builder .e-group-body .e-horizontal-mode .e-rule-delete {
  display: inline-block;
  margin-left: 8px;
}

.e-query-builder.e-device .e-group-body .e-horizontal-mode .e-rule-filter, .e-query-builder.e-device .e-group-body .e-horizontal-mode .e-rule-operator, .e-query-builder.e-device .e-group-body .e-horizontal-mode .e-rule-value, .e-query-builder.e-device .e-group-body .e-horizontal-mode .e-rule-value-delete, .e-bigger.e-query-builder .e-group-body .e-horizontal-mode .e-rule-filter, .e-bigger.e-query-builder .e-group-body .e-horizontal-mode .e-rule-operator, .e-bigger.e-query-builder .e-group-body .e-horizontal-mode .e-rule-value, .e-bigger.e-query-builder .e-group-body .e-horizontal-mode .e-rule-value-delete, .e-bigger .e-query-builder .e-group-body .e-horizontal-mode .e-rule-filter, .e-bigger .e-query-builder .e-group-body .e-horizontal-mode .e-rule-operator, .e-bigger .e-query-builder .e-group-body .e-horizontal-mode .e-rule-value, .e-bigger .e-query-builder .e-group-body .e-horizontal-mode .e-rule-value-delete {
  display: inline-block;
}

.e-query-builder.e-device .e-group-body .e-rule-field.e-btn.e-small.e-round, .e-query-builder.e-device .e-group-body .e-rule-delete.e-btn.e-small.e-round, .e-bigger.e-query-builder .e-group-body .e-rule-field.e-btn.e-small.e-round, .e-bigger.e-query-builder .e-group-body .e-rule-delete.e-btn.e-small.e-round, .e-bigger .e-query-builder .e-group-body .e-rule-field.e-btn.e-small.e-round, .e-bigger .e-query-builder .e-group-body .e-rule-delete.e-btn.e-small.e-round {
  box-shadow: none;
}

.e-query-builder.e-device .e-summary-text, .e-bigger.e-query-builder .e-summary-text, .e-bigger .e-query-builder .e-summary-text {
  font-size: 16px;
  line-height: 1.5em;
  padding: 16px;
}

.e-query-builder.e-device .e-summary-btndiv, .e-bigger.e-query-builder .e-summary-btndiv, .e-bigger .e-query-builder .e-summary-btndiv {
  padding: 8px 0 5px 8px;
  text-align: right;
}

.e-control.e-device .e-rule-value {
  width: 100%;
}

.e-control.e-bigger .e-group-body .e-rule-container .e-rule-filter,
.e-control.e-bigger .e-group-body .e-rule-container .e-operator,
.e-control.e-bigger .e-group-body .e-rule-container .e-value {
  padding: 16px 0 16px 16px;
}

.e-query-builder {
  background-color: #303030;
  border-color: #616161;
}

.e-query-builder .e-group-header .e-btn-group {
  border-color: #616161;
}

.e-query-builder .e-group-container {
  border-color: #616161;
}

.e-query-builder .e-rule-container {
  background-color: #424242;
  border-color: #616161;
}

.e-query-builder .e-rule-container.e-joined-rule {
  border-top-color: #616161;
}

.e-query-builder .e-rule-list .e-group-container {
  background-color: #303030;
}

.e-query-builder .e-rule-list > ::after, .e-query-builder .e-rule-list > ::before {
  border-color: #616161;
}

.e-query-builder .e-btn-group input:checked + label.e-btn {
  background: #00b0ff;
  border-color: #00b0ff;
  color: #000;
}

.e-query-builder .e-removerule.e-btn.e-round {
  background-color: #424242;
}

.e-query-builder .e-summary-content textarea {
  background-color: #303030;
  color: #fff;
}

.e-query-builder .e-collapse-rule {
  background-color: #303030;
  border-color: rgba(0, 0, 0, 0.12);
  color: #fff;
}

.e-query-builder .e-collapse-rule:focus, .e-query-builder .e-collapse-rule:active, .e-query-builder .e-collapse-rule:hover {
  background-color: #616161;
}

.e-tooltip-wrap.e-querybuilder-error, .e-control.e-tooltip-wrap.e-popup.e-querybuilder-error {
  background-color: #fcdbe4;
  border-color: #fcdbe4;
}

.e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-inner.e-tip-top, .e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-outer.e-tip-top {
  border-bottom-color: #fcdbe4;
  color: #fcdbe4;
}

.e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-outer.e-tip-bottom, .e-tooltip-wrap.e-querybuilder-error .e-arrow-tip-inner.e-tip-bottom {
  border-top-color: #fcdbe4;
  color: #fcdbe4;
}

.e-tooltip-wrap.e-querybuilder-error .e-tip-content, .e-tooltip-wrap.e-querybuilder-error .e-tip-content label {
  color: #f44336;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
