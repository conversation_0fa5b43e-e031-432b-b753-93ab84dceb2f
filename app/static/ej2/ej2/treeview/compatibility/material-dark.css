@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
@keyframes e-input-ripple {
  100% {
    opacity: 0;
    transform: scale(4);
  }
}

.e-control.e-treeview .e-list-item div.e-icons.interaction {
  -webkit-transition: -webkit-transform .3s ease-in-out;
  border-radius: 15px;
  transition: transform .3s ease-in-out;
}

.e-control.e-treeview .e-list-item .e-icons.e-icon-collapsible {
  transform: rotate(90deg);
}

.e-control.e-treeview.e-drag-item.e-rtl .e-icons.e-drop-next {
  transform: rotate(180deg);
}

.e-control.e-treeview.e-rtl div.e-icons {
  transform: rotate(180deg);
}

/*! TreeView icons */
.e-control.e-treeview .e-list-item div.e-icons::before {
  content: '\e22f';
}

.e-control.e-treeview .e-sibling::before {
  content: '';
}

.e-control.e-treeview .e-popup .e-icons::before {
  content: '\e930';
}

.e-control.e-treeview.e-drag-item .e-icons.e-drop-in::before {
  content: '\e22c';
}

.e-control.e-treeview.e-drag-item .e-icons.e-drop-out::before {
  content: '\e22b';
}

.e-control.e-treeview.e-drag-item .e-icons.e-drop-next::before {
  content: '\e22d';
}

.e-control.e-treeview.e-drag-item .e-icons.e-no-drop::before {
  content: '\e22a';
}

@keyframes rotation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}

.e-bigger .e-control.e-treeview .e-fullrow, .e-control.e-treeview.e-bigger .e-fullrow {
  height: 40px;
}

.e-bigger .e-control.e-treeview .e-list-text, .e-control.e-treeview.e-bigger .e-list-text {
  line-height: 38px;
  min-height: 38px;
  padding: 0 10px;
}

.e-bigger .e-control.e-treeview .e-list-text .e-input-group, .e-control.e-treeview.e-bigger .e-list-text .e-input-group {
  height: 38px;
}

.e-bigger .e-control.e-treeview .e-checkbox-wrapper, .e-control.e-treeview.e-bigger .e-checkbox-wrapper {
  margin: 0 0 0 10px;
}

.e-bigger .e-control.e-treeview .e-checkbox-wrapper + .e-list-icon, .e-bigger .e-control.e-treeview .e-checkbox-wrapper + .e-list-img, .e-control.e-treeview.e-bigger .e-checkbox-wrapper + .e-list-icon, .e-control.e-treeview.e-bigger .e-checkbox-wrapper + .e-list-img {
  margin: 0 0 0 16px;
}

.e-bigger .e-control.e-treeview .e-list-icon, .e-bigger .e-control.e-treeview .e-list-img, .e-control.e-treeview.e-bigger .e-list-icon, .e-control.e-treeview.e-bigger .e-list-img {
  margin: 0 0 0 10px;
}

.e-bigger .e-control.e-treeview .e-list-icon + .e-list-icon, .e-bigger .e-control.e-treeview .e-list-icon + .e-list-img, .e-bigger .e-control.e-treeview .e-list-img + .e-list-icon, .e-bigger .e-control.e-treeview .e-list-img + .e-list-img, .e-control.e-treeview.e-bigger .e-list-icon + .e-list-icon, .e-control.e-treeview.e-bigger .e-list-icon + .e-list-img, .e-control.e-treeview.e-bigger .e-list-img + .e-list-icon, .e-control.e-treeview.e-bigger .e-list-img + .e-list-img {
  margin: 0 0 0 10px;
}

.e-bigger .e-control.e-treeview .e-icon-collapsible::before, .e-bigger .e-control.e-treeview .e-icon-expandable::before, .e-control.e-treeview.e-bigger .e-icon-collapsible::before, .e-control.e-treeview.e-bigger .e-icon-expandable::before {
  padding: 6px;
}

.e-bigger .e-control.e-treeview.e-rtl .e-checkbox-wrapper, .e-control.e-treeview.e-bigger.e-rtl .e-checkbox-wrapper {
  margin: 0 10px 0 0;
}

.e-bigger .e-control.e-treeview.e-rtl .e-checkbox-wrapper + .e-list-icon, .e-bigger .e-control.e-treeview.e-rtl .e-checkbox-wrapper + .e-list-img, .e-control.e-treeview.e-bigger.e-rtl .e-checkbox-wrapper + .e-list-icon, .e-control.e-treeview.e-bigger.e-rtl .e-checkbox-wrapper + .e-list-img {
  margin: 0 16px 0 0;
}

.e-bigger .e-control.e-treeview.e-rtl .e-list-icon, .e-bigger .e-control.e-treeview.e-rtl .e-list-img, .e-control.e-treeview.e-bigger.e-rtl .e-list-icon, .e-control.e-treeview.e-bigger.e-rtl .e-list-img {
  margin: 0 10px 0 0;
}

.e-bigger .e-control.e-treeview.e-rtl .e-list-icon + .e-list-icon, .e-bigger .e-control.e-treeview.e-rtl .e-list-icon + .e-list-img, .e-bigger .e-control.e-treeview.e-rtl .e-list-img + .e-list-icon, .e-bigger .e-control.e-treeview.e-rtl .e-list-img + .e-list-img, .e-control.e-treeview.e-bigger.e-rtl .e-list-icon + .e-list-icon, .e-control.e-treeview.e-bigger.e-rtl .e-list-icon + .e-list-img, .e-control.e-treeview.e-bigger.e-rtl .e-list-img + .e-list-icon, .e-control.e-treeview.e-bigger.e-rtl .e-list-img + .e-list-img {
  margin: 0 10px 0 0;
}

.e-control.e-treeview {
  display: block;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
}

.e-control.e-treeview > .e-ul {
  -webkit-overflow-scrolling: touch;
  overflow: auto;
}

.e-control.e-treeview .e-ul {
  margin: 0;
  padding: 0 0 0 24px;
}

.e-control.e-treeview li.e-node-collapsed .e-list-item .e-fullrow {
  display: none;
}

.e-control.e-treeview .e-list-item {
  list-style: none;
  padding: 2px 0;
}

.e-control.e-treeview .e-list-item .e-ul {
  margin: 2px 0 -2px;
  padding: 0 0 0 24px;
}

.e-control.e-treeview .e-list-item.e-disable > .e-text-content, .e-control.e-treeview .e-list-item.e-disable > .e-fullrow {
  -ms-touch-action: none;
  filter: alpha(opacity=50);
  opacity: .5;
  pointer-events: none;
  touch-action: none;
}

.e-control.e-treeview .e-list-item .e-icons.e-icons-spinner::before {
  content: none;
}

.e-control.e-treeview .e-icons .e-spinner-pane {
  position: relative;
}

.e-control.e-treeview .e-text-content {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  border: 1px solid;
  cursor: pointer;
  margin: 0;
  padding: 0 0 0 24px;
}

.e-control.e-treeview .e-fullrow {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  border: 1px solid;
  box-sizing: border-box;
  cursor: pointer;
  height: 32px;
  left: 0;
  overflow: hidden;
  position: absolute;
  width: 100%;
}

.e-control.e-treeview .e-checkbox-wrapper {
  margin: 0 0 0 5px;
  pointer-events: all;
  position: relative;
}

.e-control.e-treeview .e-checkbox-wrapper + .e-list-icon, .e-control.e-treeview .e-checkbox-wrapper + .e-list-img {
  margin: 0 0 0 12px;
}

.e-control.e-treeview .e-checkbox-wrapper + .e-list-text {
  padding: 0 10px;
}

.e-control.e-treeview .e-checkbox-wrapper .e-ripple-container {
  bottom: -7px;
  height: 32px;
  left: -7px;
  right: -7px;
  top: -7px;
  width: 32px;
}

.e-control.e-treeview .e-list-text {
  box-sizing: border-box;
  display: inline-block;
  line-height: 30px;
  margin: 0;
  min-height: 30px;
  padding: 0 5px;
  text-decoration: none;
  vertical-align: middle;
}

.e-control.e-treeview .e-list-text .e-input-group {
  height: 30px;
  margin-bottom: 0;
  min-width: 150px;
  vertical-align: bottom;
}

.e-control.e-treeview .e-list-icon, .e-control.e-treeview .e-list-img {
  display: inline-block;
  height: 18px;
  margin: 0 0 0 5px;
  vertical-align: middle;
  width: 18px;
}

.e-control.e-treeview .e-list-icon + .e-list-icon, .e-control.e-treeview .e-list-icon + .e-list-img, .e-control.e-treeview .e-list-img + .e-list-icon, .e-control.e-treeview .e-list-img + .e-list-img {
  margin: 0 0 0 10px;
}

.e-control.e-treeview .e-list-icon + .e-list-text, .e-control.e-treeview .e-list-img + .e-list-text {
  padding: 0 10px;
}

.e-control.e-treeview .e-icon-collapsible, .e-control.e-treeview .e-icon-expandable {
  display: inline-block;
  height: 24px;
  margin: 0 0 0 -24px;
  vertical-align: middle;
  width: 24px;
}

.e-control.e-treeview .e-icon-collapsible::before, .e-control.e-treeview .e-icon-expandable::before {
  display: inline-block;
  padding: 7px;
}

.e-control.e-treeview .e-load {
  animation: rotation .5s infinite linear;
}

.e-control.e-treeview .e-sibling {
  border-radius: 10px;
  height: 6px;
  margin-top: -5px;
  position: absolute;
  width: 6px;
  z-index: 2;
}

.e-control.e-treeview .e-text-content + .e-sibling {
  margin-top: -1px;
}

.e-control.e-treeview .e-sibling::before {
  left: 6px;
  top: 3px;
  height: 1px;
  position: absolute;
  width: 144px;
  z-index: 2;
}

.e-control.e-treeview .e-popup {
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  font-weight: normal;
  position: absolute;
  z-index: 99999;
}

.e-control.e-treeview .e-popup .e-content {
  border-radius: 4px;
  border-style: solid;
  border-width: 1px;
  font-size: 14px;
  padding: 4px;
}

.e-control.e-treeview .e-popup .e-icons {
  border: 1px solid transparent;
  cursor: pointer;
  display: inline-block;
  height: 26px;
  line-height: 18px;
  padding: 4px;
  width: 26px;
}

.e-control.e-treeview .e-popup .e-downtail::before, .e-control.e-treeview .e-popup .e-downtail::after {
  border: 10px solid transparent;
  content: '';
  height: 0;
  left: 8px;
  position: absolute;
  width: 0;
}

.e-control.e-treeview .e-popup .e-downtail::after {
  bottom: -18px;
}

.e-control.e-treeview.e-fullrow-wrap .e-text-content {
  pointer-events: none;
  position: relative;
}

.e-control.e-treeview.e-fullrow-wrap .e-icon-collapsible, .e-control.e-treeview.e-fullrow-wrap .e-icon-expandable, .e-control.e-treeview.e-fullrow-wrap .e-input, .e-control.e-treeview.e-fullrow-wrap .e-list-url {
  pointer-events: auto;
}

.e-control.e-treeview.e-drag-item {
  overflow: visible;
  z-index: 10000;
}

.e-control.e-treeview.e-drag-item .e-text-content {
  float: left;
}

.e-control.e-treeview.e-drag-item .e-icon-collapsible::before, .e-control.e-treeview.e-drag-item .e-icon-expandable::before {
  font-size: 12px;
  padding: 6px;
}

.e-control.e-treeview.e-drag-item .e-drop-count {
  border: 1px solid;
  border-radius: 15px;
  box-sizing: content-box;
  font-size: 13px;
  line-height: normal;
  min-width: 12px;
  padding: 3px 5px 4px;
  margin-left: -12px;
  position: absolute;
  text-align: center;
  top: -10px;
}

.e-control.e-treeview.e-dragging .e-text-content, .e-control.e-treeview.e-dragging .e-fullrow {
  cursor: default;
}

.e-control.e-treeview.e-rtl .e-ul {
  padding: 0 24px 0 0;
}

.e-control.e-treeview.e-rtl .e-list-item .e-ul {
  padding: 0 24px 0 0;
}

.e-control.e-treeview.e-rtl .e-text-content {
  padding: 0 24px 0 0;
}

.e-control.e-treeview.e-rtl .e-checkbox-wrapper {
  margin: 0 5px 0 0;
}

.e-control.e-treeview.e-rtl .e-checkbox-wrapper + .e-list-icon, .e-control.e-treeview.e-rtl .e-checkbox-wrapper + .e-list-img {
  margin: 0 12px 0 0;
}

.e-control.e-treeview.e-rtl .e-list-icon, .e-control.e-treeview.e-rtl .e-list-img {
  margin: 0 5px 0 0;
}

.e-control.e-treeview.e-rtl .e-list-icon + .e-list-icon, .e-control.e-treeview.e-rtl .e-list-icon + .e-list-img, .e-control.e-treeview.e-rtl .e-list-img + .e-list-icon, .e-control.e-treeview.e-rtl .e-list-img + .e-list-img {
  margin: 0 10px 0 0;
}

.e-control.e-treeview.e-rtl .e-icon-collapsible, .e-control.e-treeview.e-rtl .e-icon-expandable {
  margin: 0 -24px 0 0;
}

.e-control.e-treeview.e-rtl .e-sibling::before {
  right: 6px;
  top: 3px;
}

.e-control.e-treeview.e-rtl.e-drag-item .e-text-content {
  float: right;
}

.e-control.e-treeview.e-rtl.e-drag-item .e-drop-count {
  margin-right: -12px;
}

.e-bigger .e-control.e-treeview .e-list-text, .e-control.e-treeview.e-bigger .e-list-text {
  font-size: 14px;
}

.e-bigger .e-control.e-treeview .e-icon-collapsible::before, .e-bigger .e-control.e-treeview .e-icon-expandable::before, .e-control.e-treeview.e-bigger .e-icon-collapsible::before, .e-control.e-treeview.e-bigger .e-icon-expandable::before {
  font-size: 12px;
}

.e-control.e-treeview {
  -webkit-tap-highlight-color: transparent;
}

.e-control.e-treeview .e-text-content, .e-control.e-treeview .e-fullrow {
  border-color: transparent;
}

.e-control.e-treeview .e-list-text {
  color: #fff;
  font-size: 13px;
}

.e-control.e-treeview .e-list-icon, .e-control.e-treeview .e-list-img {
  font-size: 18px;
}

.e-control.e-treeview .e-icon-collapsible, .e-control.e-treeview .e-icon-expandable {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-treeview .e-icon-collapsible::before, .e-control.e-treeview .e-icon-expandable::before {
  font-size: 10px;
}

.e-control.e-treeview .e-list-item.e-active, .e-control.e-treeview .e-list-item.e-hover {
  background: transparent;
}

.e-control.e-treeview .e-list-item.e-hover > .e-text-content {
  color: #fff;
}

.e-control.e-treeview .e-list-item.e-hover > .e-text-content .e-list-text {
  color: #fff;
}

.e-control.e-treeview .e-list-item.e-hover > .e-text-content .e-icon-collapsible, .e-control.e-treeview .e-list-item.e-hover > .e-text-content .e-icon-expandable {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-treeview .e-list-item.e-active > .e-text-content {
  color: #00b0ff;
}

.e-control.e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
  color: #00b0ff;
}

.e-control.e-treeview .e-list-item.e-active > .e-text-content .e-icon-collapsible, .e-control.e-treeview .e-list-item.e-active > .e-text-content .e-icon-expandable {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-treeview .e-list-item.e-active.e-hover > .e-text-content {
  color: #00b0ff;
}

.e-control.e-treeview .e-list-item.e-active.e-hover > .e-text-content .e-list-text {
  color: #00b0ff;
}

.e-control.e-treeview .e-list-item.e-active.e-hover > .e-text-content .e-icon-collapsible, .e-control.e-treeview .e-list-item.e-active.e-hover > .e-text-content .e-icon-expandable {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-treeview .e-list-item.e-editing.e-active > .e-text-content, .e-control.e-treeview .e-list-item.e-editing.e-hover > .e-text-content {
  color: #fff;
}

.e-control.e-treeview .e-list-item.e-editing.e-active > .e-text-content .e-list-text, .e-control.e-treeview .e-list-item.e-editing.e-hover > .e-text-content .e-list-text {
  color: #fff;
}

.e-control.e-treeview .e-list-item.e-editing.e-active > .e-text-content .e-icon-collapsible, .e-control.e-treeview .e-list-item.e-editing.e-active > .e-text-content .e-icon-expandable, .e-control.e-treeview .e-list-item.e-editing.e-hover > .e-text-content .e-icon-collapsible, .e-control.e-treeview .e-list-item.e-editing.e-hover > .e-text-content .e-icon-expandable {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-treeview .e-list-item.e-hover > .e-fullrow {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: transparent;
}

.e-control.e-treeview .e-list-item.e-active > .e-fullrow {
  background-color: rgba(255, 255, 255, 0.18);
  border-color: transparent;
}

.e-control.e-treeview .e-list-item.e-active.e-animation-active > .e-fullrow {
  background-color: transparent;
  border-color: transparent;
}

.e-control.e-treeview .e-list-item.e-active.e-animation-active > .e-text-content {
  color: #fff;
}

.e-control.e-treeview .e-list-item.e-active.e-animation-active > .e-text-content .e-list-text {
  color: #fff;
}

.e-control.e-treeview .e-list-item.e-active.e-hover > .e-fullrow {
  background-color: rgba(255, 255, 255, 0.18);
  border-color: transparent;
}

.e-control.e-treeview .e-list-item.e-editing.e-active > .e-fullrow, .e-control.e-treeview .e-list-item.e-editing.e-hover > .e-fullrow {
  background-color: transparent;
  border-color: transparent;
}

.e-control.e-treeview .e-list-item.e-disable > .e-text-content, .e-control.e-treeview .e-list-item.e-disable > .e-fullrow {
  color: rgba(255, 255, 255, 0.3);
}

.e-control.e-treeview .e-list-item.e-disable .e-icon-collapsible, .e-control.e-treeview .e-list-item.e-disable .e-icon-expandable {
  color: rgba(255, 255, 255, 0.3);
}

.e-control.e-treeview .e-sibling {
  background: #00b0ff;
}

.e-control.e-treeview .e-sibling::before {
  background: rgba(255, 255, 255, 0.6);
}

.e-control.e-treeview .e-popup .e-content {
  background-color: #616161;
  border-color: transparent;
}

.e-control.e-treeview .e-popup.e-select .e-icons {
  border-color: transparent;
}

.e-control.e-treeview .e-popup .e-downtail::before {
  border-top-color: transparent;
}

.e-control.e-treeview .e-popup .e-downtail::after {
  border-top-color: #616161;
}

.e-control.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-hover > .e-text-content {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: transparent;
}

.e-control.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-active > .e-text-content {
  background-color: rgba(255, 255, 255, 0.18);
  border-color: transparent;
}

.e-control.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-active.e-hover > .e-text-content {
  background-color: rgba(255, 255, 255, 0.18);
  border-color: transparent;
}

.e-control.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-editing.e-active > .e-text-content, .e-control.e-treeview:not(.e-fullrow-wrap) .e-list-item.e-editing.e-hover > .e-text-content {
  background-color: transparent;
  border-color: transparent;
}

.e-control.e-treeview.e-fullrow-wrap .e-text-content {
  border-color: transparent;
}

.e-control.e-treeview.e-drag-item {
  background-color: #616161;
  font-family: "Roboto", "Segoe UI", "GeezaPro", "DejaVu Serif", "sans-serif", "-apple-system", "BlinkMacSystemFont";
}

.e-control.e-treeview.e-drag-item .e-icon-collapsible::before, .e-control.e-treeview.e-drag-item .e-icon-expandable::before {
  font-size: 12px;
}

.e-control.e-treeview.e-drag-item .e-list-text {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-treeview.e-drag-item .e-icons {
  color: rgba(255, 255, 255, 0.7);
}

.e-control.e-treeview.e-drag-item .e-drop-count {
  background-color: #00b0ff;
  border-color: #000;
  color: #000;
}

.e-control.e-treeview.e-drag-item.e-rtl .e-sibling {
  background: #00b0ff;
}

.e-control .e-js [class^='e-'], .e-control .e-js [class*=' e-'] {
  box-sizing: content-box;
}
