@import url("https://fonts.googleapis.com/css?family=Roboto:400,500");
/*! switch layout */
.e-switch-wrapper,
.e-css.e-switch-wrapper {
  cursor: pointer;
  display: inline-block;
  height: 12px;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 34px;
}

.e-switch-wrapper .e-switch,
.e-css.e-switch-wrapper .e-switch {
  -moz-appearance: none;
  height: 1px;
  opacity: 0;
  position: absolute;
  width: 1px;
}

.e-switch-wrapper .e-switch-inner,
.e-css.e-switch-wrapper .e-switch-inner {
  -ms-transition: all 0.08s linear;
  -webkit-transition: all 0.08s linear;
  border: none;
  border-radius: 20px;
  box-sizing: border-box;
  height: 100%;
  left: 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  transition: all 0.08s linear;
  width: 100%;
}

.e-switch-wrapper .e-switch-on,
.e-switch-wrapper .e-switch-off,
.e-css.e-switch-wrapper .e-switch-on,
.e-css.e-switch-wrapper .e-switch-off {
  -ms-transition: transform 90ms cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition: transform 90ms cubic-bezier(0.4, 0, 0.2, 1);
  -ms-flex-align: center;
      align-items: center;
  border-radius: inherit;
  display: -ms-flexbox;
  display: flex;
  font-family: "";
  font-size: small;
  height: 100%;
  -ms-flex-pack: center;
      justify-content: center;
  left: 0;
  position: absolute;
  transition: transform 90ms cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
}

.e-switch-wrapper .e-switch-on,
.e-css.e-switch-wrapper .e-switch-on {
  left: -100%;
  text-indent: -9999px;
}

.e-switch-wrapper .e-switch-off,
.e-css.e-switch-wrapper .e-switch-off {
  left: 0;
  opacity: 0.42;
  text-indent: -9999px;
}

.e-switch-wrapper .e-switch-handle,
.e-css.e-switch-wrapper .e-switch-handle {
  -ms-transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  border-radius: 50%;
  bottom: 0;
  height: 18px;
  left: 0;
  margin: auto 0;
  position: absolute;
  top: 0;
  transition: all 0.2s linear;
  width: 18px;
}

.e-switch-wrapper .e-switch-inner.e-switch-active .e-switch-on,
.e-css.e-switch-wrapper .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
  opacity: 0.54;
}

.e-switch-wrapper .e-switch-inner.e-switch-active .e-switch-off,
.e-css.e-switch-wrapper .e-switch-inner.e-switch-active .e-switch-off {
  left: 100%;
}

.e-switch-wrapper .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -18px;
}

.e-switch-wrapper.e-switch-disabled,
.e-css.e-switch-wrapper.e-switch-disabled {
  cursor: default;
}

.e-switch-wrapper .e-ripple-container,
.e-css.e-switch-wrapper .e-ripple-container {
  border-radius: 50%;
  bottom: -9px;
  height: 52px;
  left: -17px;
  pointer-events: none;
  position: absolute;
  top: -17px;
  width: 52px;
  z-index: 1;
}

.e-switch-wrapper.e-rtl .e-switch-handle,
.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  left: 100%;
  margin-left: -18px;
}

.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-switch-wrapper.e-rtl .e-switch-on,
.e-css.e-switch-wrapper.e-rtl .e-switch-on {
  left: 100%;
}

.e-switch-wrapper.e-rtl .e-switch-off,
.e-css.e-switch-wrapper.e-rtl .e-switch-off {
  left: 0;
}

.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active {
  border-radius: 50%;
  height: 18px;
  left: 0;
  margin: auto 0;
  position: absolute;
  top: 0;
  transition: all 0.2s linear;
  width: 18px;
}

.e-switch-wrapper.e-small,
.e-css.e-switch-wrapper.e-small {
  height: 10px;
  width: 26px;
}

.e-switch-wrapper.e-small .e-switch-handle,
.e-css.e-switch-wrapper.e-small .e-switch-handle {
  height: 16px;
  width: 16px;
}

.e-switch-wrapper.e-small .e-ripple-container,
.e-css.e-switch-wrapper.e-small .e-ripple-container {
  border-radius: 50%;
  height: 36px;
  left: -10px;
  pointer-events: none;
  position: absolute;
  top: -10px;
  width: 36px;
  z-index: 1;
}

.e-switch-wrapper.e-small .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper.e-small .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -16px;
}

.e-switch-wrapper.e-small .e-switch-on,
.e-switch-wrapper.e-small .e-switch-off,
.e-css.e-switch-wrapper.e-small .e-switch-on,
.e-css.e-switch-wrapper.e-small .e-switch-off {
  font-size: 9px;
}

.e-switch-wrapper.e-small .e-switch-on,
.e-css.e-switch-wrapper.e-small .e-switch-on {
  text-indent: -9999px;
}

.e-switch-wrapper.e-small .e-switch-off,
.e-css.e-switch-wrapper.e-small .e-switch-off {
  text-indent: -9999px;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-handle,
.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle {
  left: 100%;
  margin-left: -16px;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-handle,
.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle {
  height: 16px;
  width: 16px;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-on,
.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-on {
  left: 100%;
  opacity: 0.54;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-off,
.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-off {
  left: 0;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active {
  left: 16px;
}

.e-small .e-switch-wrapper,
.e-small.e-switch-wrapper,
.e-small .e-css.e-switch-wrapper,
.e-small.e-css.e-switch-wrapper {
  height: 10px;
  width: 26px;
}

.e-small .e-switch-wrapper .e-switch-handle,
.e-small.e-switch-wrapper .e-switch-handle,
.e-small .e-css.e-switch-wrapper .e-switch-handle,
.e-small.e-css.e-switch-wrapper .e-switch-handle {
  height: 16px;
  width: 16px;
}

.e-small .e-switch-wrapper .e-ripple-container,
.e-small.e-switch-wrapper .e-ripple-container,
.e-small .e-css.e-switch-wrapper .e-ripple-container,
.e-small.e-css.e-switch-wrapper .e-ripple-container {
  border-radius: 50%;
  height: 36px;
  left: -10px;
  pointer-events: none;
  position: absolute;
  top: -10px;
  width: 36px;
  z-index: 1;
}

.e-small .e-switch-wrapper .e-switch-handle.e-switch-active,
.e-small.e-switch-wrapper .e-switch-handle.e-switch-active,
.e-small .e-css.e-switch-wrapper .e-switch-handle.e-switch-active,
.e-small.e-css.e-switch-wrapper .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -16px;
}

.e-small .e-switch-wrapper .e-switch-on,
.e-small .e-switch-wrapper .e-switch-off,
.e-small.e-switch-wrapper .e-switch-on,
.e-small.e-switch-wrapper .e-switch-off,
.e-small .e-css.e-switch-wrapper .e-switch-on,
.e-small .e-css.e-switch-wrapper .e-switch-off,
.e-small.e-css.e-switch-wrapper .e-switch-on,
.e-small.e-css.e-switch-wrapper .e-switch-off {
  font-size: 9px;
}

.e-small .e-switch-wrapper .e-switch-on,
.e-small.e-switch-wrapper .e-switch-on,
.e-small .e-css.e-switch-wrapper .e-switch-on,
.e-small.e-css.e-switch-wrapper .e-switch-on {
  text-indent: -9999px;
}

.e-small .e-switch-wrapper .e-switch-off,
.e-small.e-switch-wrapper .e-switch-off,
.e-small .e-css.e-switch-wrapper .e-switch-off,
.e-small.e-css.e-switch-wrapper .e-switch-off {
  text-indent: -9999px;
}

.e-small .e-switch-wrapper.e-rtl .e-switch-handle,
.e-small.e-switch-wrapper.e-rtl .e-switch-handle,
.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle,
.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  left: 100%;
  margin-left: -16px;
}

.e-small .e-switch-wrapper.e-rtl .e-switch-handle,
.e-small.e-switch-wrapper.e-rtl .e-switch-handle,
.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle,
.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  height: 16px;
  width: 16px;
}

.e-small .e-switch-wrapper.e-rtl .e-switch-on,
.e-small.e-switch-wrapper.e-rtl .e-switch-on,
.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-on,
.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-on {
  left: 100%;
  opacity: 0.54;
}

.e-small .e-switch-wrapper.e-rtl .e-switch-off,
.e-small.e-switch-wrapper.e-rtl .e-switch-off,
.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-off,
.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-off {
  left: 0;
}

.e-small .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-small.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-small .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-small.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-small .e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-small.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active {
  left: 16px;
}

.e-bigger.e-small .e-switch-wrapper,
.e-bigger.e-small.e-switch-wrapper,
.e-bigger.e-small .e-css.e-switch-wrapper,
.e-bigger.e-small.e-css.e-switch-wrapper {
  height: 12px;
  width: 34px;
}

.e-bigger.e-small .e-switch-wrapper .e-switch-handle,
.e-bigger.e-small.e-switch-wrapper .e-switch-handle,
.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-handle,
.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-handle {
  height: 18px;
  left: 0;
  top: 0;
  width: 18px;
}

.e-bigger.e-small .e-switch-wrapper .e-ripple-container,
.e-bigger.e-small.e-switch-wrapper .e-ripple-container,
.e-bigger.e-small .e-css.e-switch-wrapper .e-ripple-container,
.e-bigger.e-small.e-css.e-switch-wrapper .e-ripple-container {
  border-radius: 50%;
  height: 50px;
  left: -16px;
  pointer-events: none;
  position: absolute;
  top: -16px;
  width: 50px;
  z-index: 1;
}

.e-bigger.e-small .e-switch-wrapper .e-switch-handle.e-switch-active,
.e-bigger.e-small.e-switch-wrapper .e-switch-handle.e-switch-active,
.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-handle.e-switch-active,
.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -18px;
}

.e-bigger.e-small .e-switch-wrapper .e-switch-on,
.e-bigger.e-small .e-switch-wrapper .e-switch-off,
.e-bigger.e-small.e-switch-wrapper .e-switch-on,
.e-bigger.e-small.e-switch-wrapper .e-switch-off,
.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-on,
.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-off,
.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-on,
.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-off {
  font-size: 9px;
}

.e-bigger.e-small .e-switch-wrapper .e-switch-on,
.e-bigger.e-small.e-switch-wrapper .e-switch-on,
.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-on,
.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-on {
  text-indent: -9999px;
}

.e-bigger.e-small .e-switch-wrapper .e-switch-off,
.e-bigger.e-small.e-switch-wrapper .e-switch-off,
.e-bigger.e-small .e-css.e-switch-wrapper .e-switch-off,
.e-bigger.e-small.e-css.e-switch-wrapper .e-switch-off {
  text-indent: -9999px;
}

.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-handle,
.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-handle,
.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle,
.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  left: 100%;
  margin-left: -18px;
}

.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-handle,
.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-handle,
.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle,
.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  height: 18px;
  width: 18px;
}

.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-on,
.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-on,
.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-on,
.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-on {
  left: 100%;
  opacity: 0.54;
}

.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-off,
.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-off,
.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-off,
.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-off {
  left: 0;
}

.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-bigger.e-small .e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-bigger.e-small.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-bigger.e-small .e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-bigger.e-small.e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active {
  left: 18px;
}

.e-bigger .e-switch-wrapper,
.e-bigger.e-switch-wrapper,
.e-bigger .e-css.e-switch-wrapper,
.e-bigger.e-css.e-switch-wrapper {
  height: 14px;
  width: 36px;
}

.e-bigger .e-switch-wrapper .e-switch-handle,
.e-bigger.e-switch-wrapper .e-switch-handle,
.e-bigger .e-css.e-switch-wrapper .e-switch-handle,
.e-bigger.e-css.e-switch-wrapper .e-switch-handle {
  height: 20px;
  left: 0;
  top: 0;
  width: 20px;
}

.e-bigger .e-switch-wrapper .e-switch-handle.e-switch-active,
.e-bigger.e-switch-wrapper .e-switch-handle.e-switch-active,
.e-bigger .e-css.e-switch-wrapper .e-switch-handle.e-switch-active,
.e-bigger.e-css.e-switch-wrapper .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -20px;
}

.e-bigger .e-switch-wrapper .e-switch-on,
.e-bigger .e-switch-wrapper .e-switch-off,
.e-bigger.e-switch-wrapper .e-switch-on,
.e-bigger.e-switch-wrapper .e-switch-off,
.e-bigger .e-css.e-switch-wrapper .e-switch-on,
.e-bigger .e-css.e-switch-wrapper .e-switch-off,
.e-bigger.e-css.e-switch-wrapper .e-switch-on,
.e-bigger.e-css.e-switch-wrapper .e-switch-off {
  font-size: 0;
}

.e-bigger .e-switch-wrapper .e-switch-on,
.e-bigger.e-switch-wrapper .e-switch-on,
.e-bigger .e-css.e-switch-wrapper .e-switch-on,
.e-bigger.e-css.e-switch-wrapper .e-switch-on {
  text-indent: -9999px;
}

.e-bigger .e-switch-wrapper .e-switch-off,
.e-bigger.e-switch-wrapper .e-switch-off,
.e-bigger .e-css.e-switch-wrapper .e-switch-off,
.e-bigger.e-css.e-switch-wrapper .e-switch-off {
  text-indent: -9999px;
}

.e-bigger .e-switch-wrapper .e-ripple-container,
.e-bigger.e-switch-wrapper .e-ripple-container,
.e-bigger .e-css.e-switch-wrapper .e-ripple-container,
.e-bigger.e-css.e-switch-wrapper .e-ripple-container {
  height: 52px;
  left: -16px;
  top: -16px;
  width: 52px;
}

.e-bigger .e-switch-wrapper.e-rtl .e-switch-handle,
.e-bigger.e-switch-wrapper.e-rtl .e-switch-handle,
.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-handle,
.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  height: 20px;
  left: 100%;
  margin-left: -20px;
  top: 0;
  width: 20px;
}

.e-bigger .e-switch-wrapper.e-rtl .e-switch-on,
.e-bigger.e-switch-wrapper.e-rtl .e-switch-on,
.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-on,
.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-on {
  left: 100%;
  opacity: 0.54;
}

.e-bigger .e-switch-wrapper.e-rtl .e-switch-off,
.e-bigger.e-switch-wrapper.e-rtl .e-switch-off,
.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-off,
.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-off {
  left: 0;
}

.e-bigger .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-bigger.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-bigger .e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-bigger.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-bigger .e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-bigger.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-bigger .e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-bigger.e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active {
  left: 20px;
}

.e-bigger .e-switch-wrapper.e-small,
.e-bigger.e-switch-wrapper.e-small,
.e-bigger .e-css.e-switch-wrapper.e-small,
.e-bigger.e-css.e-switch-wrapper.e-small {
  height: 12px;
  width: 34px;
}

.e-bigger .e-switch-wrapper.e-small .e-switch-handle,
.e-bigger.e-switch-wrapper.e-small .e-switch-handle,
.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-handle,
.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-handle {
  height: 18px;
  left: 0;
  top: 0;
  width: 18px;
}

.e-bigger .e-switch-wrapper.e-small .e-ripple-container,
.e-bigger.e-switch-wrapper.e-small .e-ripple-container,
.e-bigger .e-css.e-switch-wrapper.e-small .e-ripple-container,
.e-bigger.e-css.e-switch-wrapper.e-small .e-ripple-container {
  border-radius: 50%;
  height: 50px;
  left: -16px;
  pointer-events: none;
  position: absolute;
  top: -16px;
  width: 50px;
  z-index: 1;
}

.e-bigger .e-switch-wrapper.e-small .e-switch-handle.e-switch-active,
.e-bigger.e-switch-wrapper.e-small .e-switch-handle.e-switch-active,
.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-handle.e-switch-active,
.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-handle.e-switch-active {
  left: 100%;
  margin-left: -18px;
}

.e-bigger .e-switch-wrapper.e-small .e-switch-on,
.e-bigger .e-switch-wrapper.e-small .e-switch-off,
.e-bigger.e-switch-wrapper.e-small .e-switch-on,
.e-bigger.e-switch-wrapper.e-small .e-switch-off,
.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-on,
.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-off,
.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-on,
.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-off {
  font-size: 9px;
}

.e-bigger .e-switch-wrapper.e-small .e-switch-on,
.e-bigger.e-switch-wrapper.e-small .e-switch-on,
.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-on,
.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-on {
  text-indent: -9999px;
}

.e-bigger .e-switch-wrapper.e-small .e-switch-off,
.e-bigger.e-switch-wrapper.e-small .e-switch-off,
.e-bigger .e-css.e-switch-wrapper.e-small .e-switch-off,
.e-bigger.e-css.e-switch-wrapper.e-small .e-switch-off {
  text-indent: -9999px;
}

.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-handle,
.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-handle,
.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle,
.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle {
  left: 100%;
  margin-left: -18px;
}

.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-handle,
.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-handle,
.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle,
.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle {
  height: 18px;
  width: 18px;
}

.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-on,
.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-on,
.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-on,
.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-on {
  left: 100%;
  opacity: 0.54;
}

.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-off,
.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-off,
.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-off,
.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-off {
  left: 0;
}

.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on,
.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-on {
  left: 0;
}

.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  left: -100%;
}

.e-bigger .e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active,
.e-bigger.e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active,
.e-bigger .e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active,
.e-bigger.e-css.e-switch-wrapper.e-small.e-rtl .e-switch-handle.e-switch-active {
  left: 18px;
}

/*! switch theme */
.e-switch-wrapper,
.e-css.e-switch-wrapper {
  -webkit-tap-highlight-color: transparent;
}

.e-switch-wrapper .e-switch-off,
.e-css.e-switch-wrapper .e-switch-off {
  background-color: #000;
  color: #fff;
}

.e-switch-wrapper .e-switch-handle,
.e-css.e-switch-wrapper .e-switch-handle {
  background-color: #f5f5f5;
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.e-switch-wrapper .e-switch-on,
.e-css.e-switch-wrapper .e-switch-on {
  background-color: #e3165b;
  color: #fff;
}

.e-switch-wrapper .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper .e-switch-handle.e-switch-active {
  background-color: #e3165b;
}

.e-switch-wrapper .e-switch-inner.e-switch-active,
.e-css.e-switch-wrapper .e-switch-inner.e-switch-active {
  background-color: #fff;
  border-color: transparent;
}

.e-switch-wrapper .e-switch-inner,
.e-css.e-switch-wrapper .e-switch-inner {
  background-color: initial;
}

.e-switch-wrapper .e-ripple-element,
.e-css.e-switch-wrapper .e-ripple-element {
  background-color: rgba(0, 0, 0, 0.12);
}

.e-switch-wrapper .e-ripple-check .e-ripple-element,
.e-css.e-switch-wrapper .e-ripple-check .e-ripple-element {
  background-color: rgba(227, 22, 91, 0.12);
}

.e-switch-wrapper.e-switch-disabled .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper.e-switch-disabled .e-switch-handle.e-switch-active {
  box-shadow: none;
}

.e-switch-wrapper.e-switch-disabled .e-switch-handle,
.e-css.e-switch-wrapper.e-switch-disabled .e-switch-handle {
  background-color: #bdbdbd;
  box-shadow: none;
}

.e-switch-wrapper.e-switch-disabled .e-switch-inner .e-switch-off,
.e-css.e-switch-wrapper.e-switch-disabled .e-switch-inner .e-switch-off {
  background-color: #000;
  border-color: #bdbdbd;
  color: transparent;
  opacity: 0.12;
}

.e-switch-wrapper.e-switch-disabled .e-switch-inner .e-switch-on,
.e-css.e-switch-wrapper.e-switch-disabled .e-switch-inner .e-switch-on {
  background-color: #000;
  color: transparent;
  opacity: 0.12;
}

.e-switch-wrapper.e-switch-disabled .e-switch-inner,
.e-css.e-switch-wrapper.e-switch-disabled .e-switch-inner {
  background-color: #000;
  border-color: transparent;
  opacity: 0.12;
}

.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner.e-switch-active,
.e-css.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner.e-switch-active {
  background-color: #000;
  border-color: transparent;
}

.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner,
.e-css.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner {
  border-color: transparent;
  color: transparent;
}

.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner.e-switch-active .e-switch-on,
.e-css.e-switch-wrapper.e-switch-disabled:hover .e-switch-inner.e-switch-active .e-switch-on {
  background-color: #000;
  color: transparent;
}

.e-switch-wrapper.e-switch-disabled:hover .e-switch-handle,
.e-css.e-switch-wrapper.e-switch-disabled:hover .e-switch-handle {
  background-color: #bdbdbd;
}

.e-switch-wrapper.e-switch-disabled:hover .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper.e-switch-disabled:hover .e-switch-handle.e-switch-active {
  background-color: #bdbdbd;
}

.e-switch-wrapper:hover .e-switch-inner.e-switch-active,
.e-css.e-switch-wrapper:hover .e-switch-inner.e-switch-active {
  background-color: transparent;
  border-color: transparent;
}

.e-switch-wrapper:hover .e-switch-inner,
.e-css.e-switch-wrapper:hover .e-switch-inner {
  background-color: none;
  border-color: inherit;
}

.e-switch-wrapper:hover .e-switch-inner.e-switch-active .e-switch-on,
.e-css.e-switch-wrapper:hover .e-switch-inner.e-switch-active .e-switch-on {
  background-color: #e3165b;
  color: #fff;
}

.e-switch-wrapper:hover .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper:hover .e-switch-handle.e-switch-active {
  background-color: #e3165b;
}

.e-switch-wrapper:not(.e-switch-disabled):hover .e-switch-handle:not(.e-switch-active),
.e-css.e-switch-wrapper:not(.e-switch-disabled):hover .e-switch-handle:not(.e-switch-active) {
  background-color: #f5f5f5;
}

.e-switch-wrapper.e-focus .e-switch-inner,
.e-css.e-switch-wrapper.e-focus .e-switch-inner {
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
  outline: none;
  outline-offset: initial;
}

.e-switch-wrapper.e-focus .e-switch-inner.e-switch-active,
.e-css.e-switch-wrapper.e-focus .e-switch-inner.e-switch-active {
  background-color: transparent;
  border-color: transparent;
  outline: none;
}

.e-switch-wrapper.e-focus .e-ripple-container,
.e-css.e-switch-wrapper.e-focus .e-ripple-container {
  background-color: rgba(0, 0, 0, 0.12);
}

.e-switch-wrapper.e-focus .e-ripple-check.e-ripple-container,
.e-css.e-switch-wrapper.e-focus .e-ripple-check.e-ripple-container {
  background-color: rgba(227, 22, 91, 0.12);
}

.e-switch-wrapper.e-rtl.e-focus .e-switch-on,
.e-css.e-switch-wrapper.e-rtl.e-focus .e-switch-on {
  background-color: rgba(227, 22, 91, 0.54);
}

.e-switch-wrapper.e-rtl.e-focus .e-switch-off,
.e-css.e-switch-wrapper.e-rtl.e-focus .e-switch-off {
  background-color: rgba(227, 22, 91, 0.54);
}

.e-switch-wrapper.e-rtl.e-focus .e-switch-inner.e-switch-active,
.e-css.e-switch-wrapper.e-rtl.e-focus .e-switch-inner.e-switch-active {
  background-color: transparent;
  border-color: transparent;
  outline: none;
}

.e-switch-wrapper.e-rtl .e-switch-on,
.e-css.e-switch-wrapper.e-rtl .e-switch-on {
  background-color: none;
}

.e-switch-wrapper.e-rtl .e-switch-handle,
.e-css.e-switch-wrapper.e-rtl .e-switch-handle {
  background-color: #f5f5f5;
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off,
.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active .e-switch-off {
  background-color: #000;
}

.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper.e-rtl .e-switch-handle.e-switch-active {
  background-color: #e3165b;
}

.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active,
.e-css.e-switch-wrapper.e-rtl .e-switch-inner.e-switch-active {
  background-color: #fff;
  border-color: transparent;
}

.e-switch-wrapper.e-rtl:hover .e-switch-inner.e-switch-active,
.e-css.e-switch-wrapper.e-rtl:hover .e-switch-inner.e-switch-active {
  background-color: transparent;
  border-color: transparent;
}

.e-switch-wrapper.e-rtl:hover .e-switch-inner,
.e-css.e-switch-wrapper.e-rtl:hover .e-switch-inner {
  border-color: inherit;
}

.e-switch-wrapper.e-rtl:hover .e-switch-inner.e-switch-active .e-switch-on,
.e-css.e-switch-wrapper.e-rtl:hover .e-switch-inner.e-switch-active .e-switch-on {
  background-color: #e3165b;
}

.e-switch-wrapper.e-rtl:hover .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper.e-rtl:hover .e-switch-handle.e-switch-active {
  background-color: #e3165b;
}

.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner .e-switch-on,
.e-css.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner .e-switch-on {
  background-color: #000;
  color: transparent;
  opacity: 0.12;
}

.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner .e-switch-off,
.e-css.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner .e-switch-off {
  background-color: #000;
  color: transparent;
  opacity: 0.12;
}

.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-handle,
.e-css.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-handle {
  background-color: #bdbdbd;
  box-shadow: none;
}

.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-handle.e-switch-active {
  background-color: #bdbdbd;
  box-shadow: none;
}

.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner,
.e-css.e-switch-wrapper.e-rtl.e-switch-disabled .e-switch-inner {
  background-color: #000;
  border-color: transparent;
  opacity: 0.12;
}

.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active .e-switch-on,
.e-css.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active .e-switch-on {
  background-color: #000;
  color: transparent;
}

.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active,
.e-css.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active {
  background-color: #000;
  border-color: transparent;
}

.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner,
.e-css.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-inner {
  border-color: transparent;
  color: transparent;
}

.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-handle.e-switch-active,
.e-css.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-handle.e-switch-active {
  background-color: #bdbdbd;
}

.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-handle,
.e-css.e-switch-wrapper.e-rtl.e-switch-disabled:hover .e-switch-handle {
  background-color: #bdbdbd;
}

.e-switch-wrapper .e-switch:focus,
.e-css.e-switch-wrapper .e-switch:focus {
  box-shadow: none;
}

.e-switch-wrapper.e-small.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active,
.e-css.e-switch-wrapper.e-small.e-rtl.e-switch-disabled:hover .e-switch-inner.e-switch-active {
  background-color: #000;
}
