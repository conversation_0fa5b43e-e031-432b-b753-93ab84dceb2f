import os
from datetime import datetime,timedelta

import app
import jwt


def jwt_encode(data, expiry=None):
    """encodes a jwt token

    Returns:
        str: The encoded string.
    """
    if expiry is None:
        expiry = 7 * 24 * 3600  # 7 days
    now = datetime.utcnow()
    lifetime = timedelta(seconds=expiry)
    expiry = now + lifetime
    payload = {'exp': expiry}
    payload.update(**data)
    token = jwt.encode(payload,os.environ.get('BOOKING_SECRET_KEY'), algorithm='HS256')
    return token
