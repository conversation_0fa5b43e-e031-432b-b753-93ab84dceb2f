from typing import Optional

from math import exp, log

from app import db
from .lookup import GliLookup
from .utils import GLIParameters, GliEthnicityFlags
from ...utils import ClippedEquationMetadata, EquationPredResult


param_mapping = {
    'VC': 'FVC',
}


class PredLmsCoefficients(db.Model):
    __tablename__ = "pred_lms_coeff"

    coefficientid = db.Column(db.Integer, primary_key=True)
    sourceid = db.Column(db.Integer)

    parameter = db.Column(db.String)
    """i.e., FVC, FEF2575, FER, FVC"""

    gender = db.Column(db.String)
    """i.e., Female, Male"""

    intercept_m = db.Column(db.Float)
    height_m = db.Column(db.Float)
    age_m = db.Column(db.Float)
    afram_m = db.Column(db.Float)
    neastasia_m = db.Column(db.Float)
    seastasia_m = db.Column(db.Float)
    othermixed_m = db.Column(db.Float)
    intercept_s = db.Column(db.Float)
    age_s = db.Column(db.Float)
    afram_s = db.Column(db.Float)
    neastasia_s = db.Column(db.Float)
    seastasia_s = db.Column(db.Float)
    othermixed_s = db.Column(db.Float)
    intercept_l = db.Column(db.Float)
    age_l = db.Column(db.Float)


class Spirometry:
    def __call__(self, param: str, metadata: 'ClippedEquationMetadata'):
        return self.calc_pred(param, metadata)

    def calc_pred(self, param: str, metadata: 'ClippedEquationMetadata') -> Optional['EquationPredResult']:
        if param in param_mapping:
            param = param_mapping[param]

        if metadata.ht_clip <= 0 or metadata.age_clip <= 0:
            return None

        splines = GliLookup.get_spirometry_splines(param, metadata)
        coeffs = PredLmsCoefficients.query.filter_by(parameter=param, gender=metadata.gender.value).first()
        ethnicity_flags = GliEthnicityFlags.from_enum(metadata.ethnicity)

        if not coeffs or not splines:
            return None

        gli_params = GLIParameters(
            l=(coeffs.intercept_l or 0) + (coeffs.age_l or 0) * log(metadata.age_clip) + splines.l_spline,
            m=exp(
                (coeffs.intercept_m or 0) +
                (coeffs.height_m or 0) * log(metadata.ht_clip) +
                (coeffs.age_m or 0) * log(metadata.age_clip) +
                (coeffs.afram_m or 0) * ethnicity_flags.black +
                (coeffs.neastasia_m or 0) * ethnicity_flags.ne_asia +
                (coeffs.seastasia_m or 0) * ethnicity_flags.se_asia +
                (coeffs.othermixed_m or 0) * ethnicity_flags.other +
                splines.m_spline
            ),
            s=exp(
                (coeffs.intercept_s or 0) +
                (coeffs.age_s or 0) * log(metadata.age_clip) +
                (coeffs.afram_s or 0) * ethnicity_flags.black +
                (coeffs.neastasia_s or 0) * ethnicity_flags.ne_asia +
                (coeffs.seastasia_s or 0) * ethnicity_flags.se_asia +
                (coeffs.othermixed_s or 0) * ethnicity_flags.other +
                splines.s_spline
            ),
        )

        return EquationPredResult(
            mpv=gli_params.mpv,
            lln=gli_params.lln,
            uln=gli_params.uln,
            range=(gli_params.lln, gli_params.uln),
            zscore_eq=f"((_result / {gli_params.m}) ** {gli_params.l} - 1) / {gli_params.l * gli_params.s}"
        )
